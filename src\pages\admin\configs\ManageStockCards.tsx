import { useEffect, useRef, useState } from "react";
import {
  But<PERSON>,
  Modal,
  Form,
  Popconfirm,
  message,
  Breadcrumb,
  Row,
  Col,
  Tag,
  Input,
  Select,
} from "antd";
import { DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { getCardTypesAll } from "../../../features/admin/cardTypeSlice.ts";
import { getAdminsAll } from "../../../features/admin/adminSlice.ts";
import {
  deleteStockCard,
  getLatestStockCard,
  getStockCards,
  storeStockCard,
} from "../../../features/admin/stockCardSlice.ts";
import { getAgentCardsAffectationAll } from "../../../features/admin/agentCardsAffectationSlice.ts";
import { useDebounce } from "../../../hooks/useDebounce.tsx";
import { useWatch } from "antd/es/form/Form";
import { toast } from "react-toastify";
import { hasPermission } from "../../../helpers/permissions.ts";

function ManageStockCards() {
  const { t } = useTranslation();

  const actionRef = useRef<any>();
  const [typeStock, setTypeStock] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [viewMode, setViewMode] = useState(false);
  const [editingStockCard, setEditingStockCard] = useState<any>(null);
  const [form] = Form.useForm();
  const [cardTypes, setCardTypes] = useState<any>([]);
  const [agents, setAgents] = useState<any>([]);
  const [agentCardsAffectations, setAgentCardsAffectations] = useState<any>([]);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [total, setTotal] = useState(1);
  const [intervalle, setIntervalle] = useState<any>([]);
  const [idTypeCard, setIdTypeCard] = useState<number>(0);
  const dispatch = useDispatch<any>();
  const sequenceStart = useWatch("sequence_start", form);
  const selectedAgent = useWatch("id_agent", form);

  const getAgentCardsAffectationsAll = async () => {
    try {
      const response = await dispatch(getAgentCardsAffectationAll()).unwrap();
      setAgentCardsAffectations(response.data);
    } catch (error) {
      console.error("Error fetching agent cards affectations:", error);
    }
  };

  useEffect(() => {
    getAgentCardsAffectationsAll();
  }, []);

  // Check if a card sequence is within an affectation sequence
  const isCardInAffectationSequence = (cardTypeId: number, start: number, end: number) => {
    if (!agentCardsAffectations || !agentCardsAffectations.length) return false;

    // Filter affectations by card type
    const relevantAffectations = agentCardsAffectations.filter((affectation: any) => {
      return affectation.card_types.some((ct: any) => ct.card_type.id === cardTypeId);
    });

    // Check if the sequence is within any affectation sequence
    return relevantAffectations.some((affectation: any) => {
      return affectation.card_types.some((ct: any) => {
        if (ct.card_type.id !== cardTypeId) return false;

        // Check if the sequence is within this affectation's sequence
        return start >= ct.start_serial_number && end <= ct.end_serial_number;
      });
    });
  };

  const validateSequence = (_: any, value: number) => {
    if (intervalle.length === 0) return Promise.resolve();

    // Range validation
    if (
      typeStock === "retour" &&
      (value < intervalle[0] || value > intervalle[1])
    ) {
      const errorMessage = t("manage_stockCards.errors.sequence_out_of_range")
        .replace("{min}", intervalle[0].toString())
        .replace("{max}", intervalle[1].toString());

      return Promise.reject(new Error(errorMessage));
    }

    // For return, check if the sequence is in an affectation
    // This is a preliminary check that will be completed when end sequence is entered
    if (typeStock === "retour" && idTypeCard && value) {
      // Find any affectation that contains this start value
      const hasAnyAffectation = agentCardsAffectations?.some((affectation: any) => {
        return affectation.card_types.some((ct: any) => {
          if (ct.card_type.id !== idTypeCard) return false;
          return value >= ct.start_serial_number && value <= ct.end_serial_number;
        });
      });

      if (!hasAnyAffectation) {
        return Promise.reject(
          new Error(t("manage_stockCards.errors.sequence_not_in_affectation") ||
            "The card sequence must be part of an affectation sequence")
        );
      }
    }

    return Promise.resolve();
  };

  const validateSequenceEnd = (_: unknown, value: number) => {
    const start = sequenceStart ?? 0;

    // Basic validation - end must be greater than or equal to start
    if (Number(value) < Number(start)) {
      return Promise.reject(
        new Error(t("manage_stockCards.errors.sequence_end_before_start"))
      );
    }

    // Range validation for return
    if (
      typeStock === "retour" &&
      (Number(value) < intervalle[0] || Number(value) > intervalle[1])
    ) {
      if (intervalle.length === 0)
        return Promise.reject(new Error("pas de stock"));
      const errorMessage = t("manage_stockCards.errors.sequence_out_of_range")
        .replace("{min}", intervalle[0].toString())
        .replace("{max}", intervalle[1].toString());

      return Promise.reject(new Error(errorMessage));
    }

    // For return, check if the sequence is in an affectation
    if (typeStock === "retour" && idTypeCard && start && value) {
      const isInAffectation = isCardInAffectationSequence(idTypeCard, Number(start), Number(value));
      if (!isInAffectation) {
        return Promise.reject(
          new Error(t("manage_stockCards.errors.sequence_not_in_affectation") ||
            "The card sequence must be part of an affectation sequence")
        );
      }
    }

    return Promise.resolve();
  };
  const getLatestStockCardRecord = async () => {
    if (typeStock === "ajout" && idTypeCard && !editingStockCard) {
      await dispatch(getLatestStockCard(idTypeCard))
        .unwrap()
        .then((originalPromiseResult: any) => {
          console.log("response", originalPromiseResult?.data);
          form.setFieldsValue({
            sequence_start: originalPromiseResult?.data?.sequence_end
              ? originalPromiseResult?.data?.sequence_end + 1
              : 1,
          });
        })
        .catch(() => form.setFieldsValue({ sequence_start: 1 }));
    }
    // else {
    //   setIntervalle([]);
    //   await dispatch(
    //     getIntervalleSequence({ id: idTypeCard, debSeq: sequenceStart })
    //   )
    //     .unwrap()
    //     .then((originalPromiseResult: any) => {
    //       console.log("response between", [
    //         originalPromiseResult?.data?.sequence_start,
    //         originalPromiseResult?.data?.sequence_end,
    //       ]);
    //       setIntervalle([
    //         originalPromiseResult?.data?.sequence_start,
    //         originalPromiseResult?.data?.sequence_end,
    //       ]);
    //     })
    //     .catch(() => {
    //       setIntervalle([]);
    //     });
    // }
  };
  const debouncedGetRecords = useDebounce(getLatestStockCardRecord, 300);

  useEffect(() => {
    debouncedGetRecords();
  }, [idTypeCard, typeStock]);

  const confirmSubmit = (values: any) => {
    const modal = Modal.confirm({
      title: t("manage_salesPoints.confirmAction"),
      content: t("manage_salesPoints.confirmAdd"),
      okText: t("common.yes"),
      cancelText: t("common.no"),
      onOk: async () => {
        modal.destroy();
        await handleFormSubmit(values);
      },
      centered: true,
    });
  };

  const columns: any = [
    {
      title: `${t("manage_stockCards.labels.id")}`,
      dataIndex: "id",
      search: false,
      width: 40,
      responsive: ["xs", "sm", "md", "lg"],
    },
    {
      title: `${t("manage_stockCards.labels.id_card_type")}`,
      dataIndex: "id_card_type",
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, data: any) => data.cardType?.nom_fr,
      valueType: "select",
      fieldProps: {
        showSearch: true,
        optionFilterProp: "children",
      },
      request: async () => {
        const response = await dispatch(getCardTypesAll()).unwrap();
        console.log(response);
        setCardTypes(response.data);
        return response.data.map((r: any) => ({
          label: r.nom_fr,
          value: r.id,
        }));
      },
    },
    {
      title: `${t("manage_stockCards.labels.id_agent")}`,
      dataIndex: "id_agent",
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, data: any) =>
        data.agent ? data.agent?.lastname + " " + data.agent?.firstname : "-",
      valueType: "select",
      fieldProps: {
        showSearch: true,
        optionFilterProp: "children",
      },
      request: async () => {
        const response = await dispatch(getAdminsAll()).unwrap();
        setAgents(response.data);
        return response.data.map((r: any) => ({
          label: r.firstname + " " + r.lastname,
          value: r.id,
        }));
      },
    },
    {
      title: t("manage_stockCards.labels.num_seq_start"),
      dataIndex: "sequence_start",
      responsive: ["xs", "sm", "md", "lg"],
    },
    {
      title: t("manage_stockCards.labels.num_seq_end"),
      dataIndex: "sequence_end",
      responsive: ["xs", "sm", "md", "lg"],
    },
    {
      title: `${t("manage_stockCards.labels.mouvement")}`,
      dataIndex: "mouvement",
      search: false,
      responsive: ["xs", "sm", "md", "lg"],
      render: (mouvement: string) => {
        return mouvement === "ajout" ? (
          <Tag color="success">{t("manage_stockCards.ajout")}</Tag>
        ) : (
          <Tag color="error">{t("manage_stockCards.retour")}</Tag>
        );
      },
    },
    {
      title: `${t("manage_stockCards.labels.actions")}`,
      fixed: "right",
      width: 120,
      search: false,
      responsive: ["xs", "sm", "md", "lg"],
      render: (_: any, record: any) => (
        <div className="flex gap-1">
          <Button
            className="btn-view"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          />
          {/* <Button
            className="btn-edit"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          /> */}
          {
            hasPermission("delete_stock_cards") && (
              <Popconfirm
                title={t("manage_stockCards.confirmDelete")}
                onConfirm={() => handleDelete(record.id)}
                okText={t("manage_stockCards.yes")}
                cancelText={t("manage_stockCards.no")}
              >
                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
              </Popconfirm>
            )
          }
        </div>
      ),
    },
  ];
  const breadcrumbItems = [
    {
      title: (
        <Link className="!bg-white" to="/auth/commercial-dashboard">
          {t("auth_sidebar.categories.sales")}
        </Link>
      ),
    },
    {
      title: t("manage_stockCards.title"),
    },
  ];

  const handleView = (record: any) => {
    setEditingStockCard(record);
    form.setFieldsValue(record);
    setTypeStock(record?.mouvement);
    setViewMode(true);
    setModalVisible(true);
  };
  const handleAdd = () => {
    setEditingStockCard(null);
    form.resetFields();
    setViewMode(false);
    setModalVisible(true);
  };

  const handleFormSubmit = async (values: any) => {
    const toastId = toast.loading(t("messages.loading"), {
      position: "top-center",
    });
    
    if (typeStock === "ajout") {
      values.id_agent = null;
    }

    // For return, perform a final check to ensure the sequence is in an affectation
    if (typeStock === "retour") {
      const start = Number(values.sequence_start);
      const end = Number(values.sequence_end);
      const cardTypeId = Number(values.id_card_type);

      if (!isCardInAffectationSequence(cardTypeId, start, end)) {
        message.error(t("manage_stockCards.errors.sequence_not_in_affectation") ||
          "The card sequence must be part of an affectation sequence");
        return;
      }
    }

    const payload = editingStockCard
      ? { id: editingStockCard.id, ...values }
      : values;

    try {
      setLoading(true);
      dispatch(storeStockCard(payload))
        .unwrap()
        .then(() => {
          toast.update(toastId, {
            render: t("messages.success"),
            type: "success",
            isLoading: false,
            autoClose: 3000,
          });
          setLoading(false);
          actionRef.current?.reload();
          handleReset();
        })
        .catch((error: any) => {
          console.log(error);
          setLoading(false);
          if (error.errors) {
            const fieldErrors = Object.entries(error.errors).map(
              ([name, errors]) => ({
                name,
                errors: Array.isArray(errors) ? errors : [errors],
              })
            );
            form.setFields(fieldErrors);
          }
          toast.update(toastId, {
            render: t("messages.error"),
            type: "error",
            isLoading: false,
            autoClose: 3000,
          });
        });
    } catch (error: any) {
      setLoading(false);
      toast.update(toastId, {
        render: t("messages.error"),
        type: "error",
        isLoading: false,
        autoClose: 3000,
      });
    }
  };

  const handleDelete = async (id: number) => {
    try {
      await dispatch(deleteStockCard(id)).unwrap();
      actionRef.current?.reload();
      console.log("SCOCK TYPE supprimé avec succès, ID :", id);
      message.success(t("messages.success"));
      actionRef.current?.reload();
    } catch (error: any) {
      message.error(error ?? t("messages.error"));
    }
  };

  const handleReset = () => {
    setModalVisible(false);
    form.resetFields();
    setTypeStock("");
    setIdTypeCard(0);
    setIntervalle([]);
  };

  const handleGetStockCards = (params: any, sort: any, filter: any) => {
    return dispatch(
      getStockCards({
        pageNumber,
        perPage: pageSize,
        params,
        sort,
        filter,
      })
    )
      .unwrap()
      .then((originalPromiseResult: any) => {
        setTotal(originalPromiseResult.meta.total);
        return originalPromiseResult.data;
      })
      .catch((rejectedValueOrSerializedError: any) => {
        console.log(rejectedValueOrSerializedError);
      });
  };

  return (
    <>
      <Breadcrumb className="mb-5" items={breadcrumbItems} />

      <Row>
        <Col span={24}>
          <ProTable
            headerTitle={t("manage_stockCards.title")}
            columns={columns}
            actionRef={actionRef}
            cardBordered
            request={async (params: any, sort: any, filter: any) => {
              setLoading(true);
              const dataFilter: any = await handleGetStockCards(
                params,
                sort,
                filter
              );
              setLoading(false);
              return {
                data: dataFilter,
                success: true,
                total: total,
              };
            }}
            pagination={{
              pageSize: pageSize,
              total: total,
              showSizeChanger: true,
              onChange: (page) => setPageNumber(page),
              onShowSizeChange: (_, pageSize) => {
                setPageSize(pageSize);
              },
            }}
            scroll={{
              x: 800,
            }}
            search={{
              labelWidth: "auto",
              className: "bg-[#FAFAFA]",
            }}
            options={{
              fullScreen: true,
            }}
            loading={loading}
            toolBarRender={() => [
              <Button key="button" onClick={handleAdd} className="btn-add">
                {t("manage_stockCards.add")}
              </Button>,
            ]}
          />
        </Col>
      </Row>

      <Modal
        width={900}
        title={
          viewMode
            ? t("manage_stockCards.details")
            : editingStockCard
            ? t("manage_stockCards.edit")
            : t("manage_stockCards.add")
        }
        open={modalVisible}
        onCancel={() => handleReset()}
        onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
        okText={viewMode ? null : t("manage_stockCards.save")}
        footer={viewMode ? null : undefined}
      >
        <Form
          className="form-inputs"
          form={form}
          layout="vertical"
          onFinish={confirmSubmit}
        >
          <Row gutter={[16, 16]}>
            <Col xs={12} sm={12}>
              <Form.Item
                label={t("manage_stockCards.labels.mouvement")}
                name="mouvement"
                rules={[
                  {
                    required: true,
                    message: t("manage_stockCards.errors.mouvementRequired"),
                  },
                ]}
              >
                <Select
                  disabled={viewMode}
                  onSelect={(value) => setTypeStock(value)}
                  placeholder={t("manage_stockCards.placeholders.mouvement")}
                >
                  <Select.Option value="ajout">
                    {t("manage_stockCards.ajout")}
                  </Select.Option>
                  <Select.Option value="retour">
                    {t("manage_stockCards.retour")}
                  </Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={12} sm={12}>
              <Form.Item
                label={t("manage_stockCards.labels.id_card_type")}
                name="id_card_type"
                rules={[
                  {
                    required: true,
                    message: t("manage_stockCards.errors.id_card_typeRequired"),
                  },
                ]}
              >
                <Select
                  placeholder={t("manage_stockCards.placeholders.cardTypes")}
                  options={cardTypes?.map((e: any) => {
                    return {
                      label: e.nom_fr,
                      value: e.id,
                    };
                  })}
                  onSelect={(value) => {
                    setIdTypeCard(value);
                  }}
                  disabled={viewMode}
                />
              </Form.Item>
            </Col>
          </Row>
          {typeStock === "retour" && (
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={24}>
                <Form.Item
                  label={t("manage_stockCards.labels.id_agent")}
                  name="id_agent"
                  rules={[
                    {
                      required: true,
                      message: t("manage_stockCards.errors.id_agentRequired"),
                    },
                  ]}
                >
                  <Select
                    options={agents?.map((e: any) => {
                      return {
                        label: e.firstname + " " + e.lastname,
                        value: e.id,
                      };
                    })}
                    disabled={viewMode}
                  />
                </Form.Item>
              </Col>
            </Row>
          )}
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                label={t("manage_stockCards.labels.num_seq_start")}
                name="sequence_start"
                rules={[
                  {
                    required: true,
                    message: t(
                      "manage_stockCards.errors.num_seq_startRequired"
                    ),
                  },
                  {
                    validator: validateSequence,
                  },
                ]}
              >
                <Input
                  type="number"
                  min={1}
                  placeholder={t(
                    "manage_stockCards.placeholders.num_seq_start"
                  )}
                  disabled={
                    typeStock !== "retour" || viewMode || editingStockCard
                  }
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label={t("manage_stockCards.labels.num_seq_end")}
                name="sequence_end"
                rules={[
                  {
                    required: true,
                    message: t("manage_stockCards.errors.num_seq_endRequired"),
                  },
                  () => ({
                    validator(_, value) {
                      console.log(value);
                      return validateSequenceEnd(_, value);
                    },
                  }),
                ]}
              >
                <Input
                  type="number"
                  placeholder={t("manage_stockCards.placeholders.num_seq_end")}
                  disabled={viewMode || editingStockCard}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
}

export default ManageStockCards;
