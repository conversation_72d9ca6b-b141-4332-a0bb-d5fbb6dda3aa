import {useEffect, useRef, useState} from "react";

import {
    <PERSON><PERSON>,
    Modal,
    Form,
    Popconfirm,
    Breadcrumb,
    Row,
    Col, Spin, Select, Radio, Input, Upload, Tag, Tooltip, Switch, Card, Divider, DatePicker, Badge,
} from "antd";
import {EditOutlined, DeleteOutlined, EyeOutlined, IdcardOutlined, DownloadOutlined} from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import dayjs from 'dayjs';
import {
    CalendarSync,
    UploadIcon,
    InfoIcon,
    UserIcon,
    CheckCircle,
    Ticket,
    WalletMinimal,
    Copy,
    UndoDot,
} from "lucide-react";
import {SellsPeriodClosedInfo, SubsCardPrintModal} from "../../../components";
import LineDisplay from "../../../components/configs/LineDisplay.tsx";
import CropModal from "../../../components/subs/CropModal.tsx";
import PaymentModal from "../../../components/subs/PaymentModal.tsx";
import {GetRestDaysData} from "../../../data/StaticData.ts";
import moment from "moment/moment";
import { useDispatch } from "react-redux";
import { getAbnTypesAll } from "../../../features/admin/abnTypeSlice.ts";
import { toast } from "react-toastify";
import { verifySocialAffairCinParent } from "../../../features/admin/socialAffairSlice.ts";
import { getStationsBySubscriptionType, getStationsForTrip } from "../../../features/admin/stationSlice.ts";
import { getLinesByStations, getLineStationsAndRoutes } from "../../../features/admin/lineSlice.ts";
import { getPeriodicityAll } from "../../../features/admin/periodicitySlice.ts";
import { getGovernorateAll } from "../../../features/admin/governorateSlice.ts";
import { getDelegationsByGovernorate } from "../../../features/admin/delegationSlice.ts";
import { getEstablishmentAll } from "../../../features/admin/establishmentSlice.ts";
import { getSchoolDegreeAll } from "../../../features/admin/schoolDegreeSlice.ts";
import { getClientTypesAll } from "../../../features/admin/clientTypeSlice.ts";
import { getSubscriptions, storeSubscription, updateSubscription, deleteSubscription, storeClientWithSubscription } from "../../../features/subs/subsSlice.ts";
import { formatImagePath } from "../../../tools/helpers.ts";
import { exportToExcel, ExportColumn } from "../../../tools/excelExport.ts";
import { getSalesPeriodsAll } from "../../../features/admin/salesPeriodsSlice.ts";
import { useSelector } from "react-redux";
import { hasPermission } from "../../../helpers/permissions.ts";
import FormItem from "antd/es/form/FormItem/index";
import { cancelTransaction } from "../../../features/subs/transactionSlice.ts";

function ManageNewSubs() {
    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;

    const actionRef = useRef<any>();
    const dispatch = useDispatch<any>();

    const permissions = JSON.parse(localStorage.getItem("PERMISSIONS") || "[]")

    const [isPasswordEditable, setPasswordEditable] = useState(false);
    const [isDuplicated,setIsDuplicated] = useState(false);
    const [filteredDelegations, setFilteredDelegations] = useState<any[]>([]);
    const [isDelegationsLoading, setIsDelegationsLoading] = useState(false);

    const [specialClient, setSpecialClient] = useState<any>();
    const [isStagiaire, setIsStagiaire] = useState(false);
    const [isImpersonalMoral, setIsImpersonalMoral] = useState(false)

    // const [isPasswordEditable, setPasswordEditable] = useState(false); // Unused
    const [isSocialAffairVerified, setIsSocialAffairVerified] = useState(false);
    const [loading, setLoading] = useState(false);
    const [loadingFetch, setLoadingFetch] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [isRenewal, setIsRenewal] = useState(false);
    const [paymentModal, setPaymentModal] = useState(false);
    const [editingNewSubs, setEditingNewSubs] = useState<any>(null);
    const [form] = Form.useForm();

    const [cropModalVisible,setCropModalVisible] = useState(false);
    const [imageSrc, setImageSrc] = useState<string | null>(null);

    const [croppedImage, setCroppedImage] = useState<string | null>(null);

    const [selectedFilterGovernorate, setSelectedFilterGovernorate] = useState<number | null>(null);
    const [selectedStationDepart, setSelectedStationDepart] = useState(null);
    const [selectedStationArrival, setSelectedStationArrival] = useState(null);
    const [selectedLine, setSelectedLine] = useState<number | null>(null);
    const [abnRecord, setAbnRecord] = useState<any>(null)
    const [selectedClient, setSelectedClient] = useState<any>(null);
    const [selectedEstablishment, setSelectedEstablishment] = useState<any>(null);
    const [isSocialAffair, setIsSocialAffair] = useState(false);
    const [isCardModalVisible, setIsCardModalVisible] = useState(false);
    const [maxDaysPerWeek , setMaxDaysPerWeek] = useState<any>(null);
    const [selectedAbnType, setSelectedAbnType] = useState<any>(null);
    const [isOpenSellsPeriod, setIsOpenSellsPeriod] = useState(false);
    const [isReversed, setIsReversed] = useState(false);

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);
    const [exportLoading, setExportLoading] = useState(false);

    // Stocker les derniers paramètres de filtrage utilisés
    const [lastUsedParams, setLastUsedParams] = useState<any>({});
    const [lastUsedSort, setLastUsedSort] = useState<any>({});
    const [lastUsedFilter, setLastUsedFilter] = useState<any>({});

    const [stations, setStations] = useState<any[]>([]);
    const [isLoadingStations, setIsLoadingStations] = useState(false);

    const [connectedStations, setConnectedStations] = useState<any[]>([]);
    const [isLoadingConnectedStations, setIsLoadingConnectedStations] = useState(false);

    const [availableLines, setAvailableLines] = useState<any[]>([]);
    const [isLoadingLines, setIsLoadingLines] = useState(false);

    const [lineDetails, setLineDetails] = useState<any>(null);
    const [isLoadingLineDetails, setIsLoadingLineDetails] = useState(false);

    // Fetch all data from store
    const governorates = useSelector((state: any) => state.governorate.items.data);
    const abnTypes = useSelector((state: any) => state.abnType.items.data);
    const periodicities = useSelector((state: any) => state.periodicity.items.data);
    const salesPeriods = useSelector((state: any) => state.salesPeriod.items.data);
    const clientTypes = useSelector((state: any) => state.clientType.items.data);
    const establishments = useSelector((state: any) => state.establishment.items.data);
    const schoolDegrees = useSelector((state: any) => state.schoolDegree.items.data);
    const [filteredDegrees, setFilteredDegrees] = useState([]);
    const [dobSelected, setDobSelected] = useState(false);


    const restDaysData:any = GetRestDaysData()

    useEffect(() => {
        if (editingNewSubs && selectedLine) {
            return;
        }
        if (selectedStationDepart && selectedStationArrival) {
            fetchLinesByStations(selectedStationDepart, selectedStationArrival, selectedAbnType?.id);
        } else {
            setAvailableLines([]);
            setSelectedLine(null);
            setLineDetails(null);
        }
    }, [selectedStationDepart, selectedStationArrival, editingNewSubs, selectedLine, selectedAbnType]);

    const fetchLinesByStations = async (departureStationId: number, arrivalStationId: number, subsTypeId = null) => {
        const typeId = subsTypeId || selectedAbnType?.id;

        if (!departureStationId || !arrivalStationId || !typeId) {
            setAvailableLines([]);
            return [];
        }
        try {
            setIsLoadingLines(true);
            const response = await dispatch(getLinesByStations({
                departureStationId,
                arrivalStationId,
                subsTypeId: typeId
            })).unwrap();
            setAvailableLines(response?.data || []);
            return response?.data || [];
        } catch (error) {
            console.error('Error fetching lines:', error);
            toast.error(t("common.errors.unexpected"));
            setAvailableLines([]);
            return [];
        } finally {
            setIsLoadingLines(false);
        }
    };

    const fetchStoreData = async () => {
        try {
            setLoadingFetch(true);

            const promises = [];

            if(!governorates?.length){
                promises.push(dispatch(getGovernorateAll()).unwrap());
            }
            if (!abnTypes?.length) {
                promises.push(dispatch(getAbnTypesAll()).unwrap());
            }
            if (!periodicities?.length) {
                promises.push(dispatch(getPeriodicityAll()).unwrap());
            }
            if (true) {
                promises.push(dispatch(getSalesPeriodsAll()).unwrap());
            }
            if (!clientTypes?.length) {
                promises.push(dispatch(getClientTypesAll()).unwrap());
            }
            if (!establishments?.length) {
                promises.push(dispatch(getEstablishmentAll()).unwrap());
            }
            if (!schoolDegrees?.length) {
                promises.push(dispatch(getSchoolDegreeAll()).unwrap());
            }

            await Promise.all(promises);
        } catch (error) {
            console.error('Error fetching initial data:', error);
            toast.error(t("common.errors.unexpected"));
        } finally {
            setLoadingFetch(false);
        }
    }

    useEffect(() => {
        setLoadingFetch(true);
        fetchStoreData();
    }, []);

    console.log(specialClient);

    const handleGovernorateChange: any = async (governorateId: number | null) => {
        form.setFieldsValue({ id_delegation: null });
        setSelectedFilterGovernorate(governorateId);
        setIsDelegationsLoading(true);
        setFilteredDelegations([]);
        if (!governorateId) {
            setFilteredDelegations([]);
            setIsDelegationsLoading(false);
            return;
        }
        try {
            const response:any = await dispatch(getDelegationsByGovernorate(governorateId)).unwrap();
            setFilteredDelegations(response.data || []);
        } catch (error) {
            console.error('Error fetching delegations:', error);
            toast.error(t("common.errors.unexpected"));
        } finally {
            setIsDelegationsLoading(false);
        }
    };

    const handleDelegationChange: any = async (delegationId: any) => {
        fetchStations(selectedAbnType?.id, delegationId);
    };

    const fetchStations = async (subsTypeId = null, delegationId = null) => {
        const typeId = subsTypeId || selectedAbnType?.id;

        if (!typeId) {
            return;
        }


        if(!selectedAbnType?.is_student){
             delegationId = null;
        }

        try {
            setIsLoadingStations(true);
            let stationsResponse;

            // Create params object with subsTypeId and optional delegationId
            const params = {
                subsTypeId: typeId,
                delegationId: delegationId || null
            };

            stationsResponse = await dispatch(getStationsBySubscriptionType(params)).unwrap();

            setStations(stationsResponse?.data || []);

            // Only reset connected stations if not in edit/view mode
            if (!editingNewSubs) {
                setConnectedStations([]);
                setSelectedStationArrival(null);
            }

            return stationsResponse?.data || [];
        } catch (error) {
            console.error('Error fetching stations:', error);
            toast.error(t("common.errors.unexpected"));
            return [];
        } finally {
            setIsLoadingStations(false);
        }
    };

    const fetchConnectedStations = async (departureStationId: number, subsTypeId = null, delegationId = null) => {
        const typeId = subsTypeId || selectedAbnType?.id;

        if (!departureStationId || !typeId) {
            setConnectedStations([]);
            return [];
        }

        if(!selectedAbnType?.is_student){
            delegationId = null;
       }

        try {
            setIsLoadingConnectedStations(true);

            const response = await dispatch(getStationsForTrip({
                stationId: departureStationId,
                subsTypeId: typeId,
                delegationId: delegationId || null
            })).unwrap();

            setConnectedStations(response?.data || []);
            return response?.data || [];
        } catch (error) {
            console.error('Error fetching connected stations:', error);
            toast.error(t("common.errors.unexpected"));
            setConnectedStations([]);
            return [];
        } finally {
            setIsLoadingConnectedStations(false);
        }
    };

    const handleAbnTypeChange = async (abnTypeId: number) => {
        if (selectedAbnType?.id === abnTypeId) {
            return;
        }

        const hasActiveSalePeriod = salesPeriods && Array.isArray(salesPeriods) ? salesPeriods.some((period:any) => {
            const now = new Date();
            const startDate = new Date(period.date_start);
            const endDate = new Date(period.date_end);
            return (period.status && period.id_abn_type === abnTypeId && now >= startDate && now <= endDate);
        }) : false;

        setIsOpenSellsPeriod(!hasActiveSalePeriod);

        form.resetFields();
        setSelectedClient(null);

        const selectedType = abnTypes.find((type:any) => type.id === abnTypeId);
        setSelectedAbnType(selectedType);

        if (selectedType && !selectedType.is_student && !selectedType.is_impersonal && !selectedType.is_conventional) {
            setSpecialClient("CIVIL");
            form.setFieldsValue({ id_subs_type: abnTypeId, special_client: "CIVIL" });
        } else {
            setSpecialClient(null);
            form.setFieldsValue({ id_subs_type: abnTypeId, special_client: null });
        }

        setStations([]);
        setConnectedStations([]);
        setIsStagiaire(false);
        setAvailableLines([]);
        setSelectedStationDepart(null);
        setSelectedStationArrival(null);
        setSelectedLine(null);
        setLineDetails(null);
        setIsSocialAffair(false);
    };

    const handleGetSubscriptions = async (params: any = {}, sort: any = {}, filter: any = {}) => {
        const isInitialDataLoaded =
            governorates?.length > 0 &&
            abnTypes?.length > 0 &&
            periodicities?.length > 0 &&
            salesPeriods?.length > 0 &&
            clientTypes?.length > 0 &&
            establishments?.length > 0 &&
            schoolDegrees?.length > 0;

        try {
            if (isInitialDataLoaded) {
                setLoadingFetch(true);
            }

            setLastUsedParams(params);
            setLastUsedSort(sort);
            setLastUsedFilter(filter);

            const response = await dispatch(getSubscriptions({
                pageNumber,
                perPage: pageSize,
                params,
                sort,
                filter
            })).unwrap();

            setTotal(response.meta?.total || 0);

            const data = response.data || [];
            data.forEach((item: any) => {
                item.key = item.id;
            });

            return {
                data,
                success: true,
                total: response.meta?.total || 0
            };
        } catch (error) {
            console.error('Error fetching subscriptions:', error);
            toast.error(t("common.errors.unexpected"));
            return {
                data: [],
                success: false,
                total: 0
            };
        } finally {
            // Only turn off loading if initial data is loaded
            if (isInitialDataLoaded) {
                setLoadingFetch(false);
            }
        }
    };



    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_newSubs.labels.client")}`,
            dataIndex: "client",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data?.client?.is_moral ? data.client.society_name : `${data.client.firstname} ${data.client.lastname}`
        },
        {
            title: `${t("manage_newSubs.labels.abnType")}`,
            dataIndex: "id_subs_type",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => {
                return data?.subs_type ? (
                    <Badge color={data.subs_type.color} text={data.subs_type[`nom_${currentLang}`]} />
                ) : '-';
            },
            valueType: "select",
            fieldProps: {
                showSearch: true,
                placeholder: t("manage_newSubs.placeholders.abnType"),
                options: abnTypes?.map((el:any) => ({
                    label: el[`nom_${currentLang}`],
                    value: el.id
                })),
                filterOption: (input: string, option?: { label: string; value: number }) =>
                    option?.label.toLowerCase().includes(input.toLowerCase()) ?? false
            },
        },
        {
            title: `${t("manage_newSubs.labels.isMoralPhy")}`,
            dataIndex: "is_moral",
            responsive: ["xs", "sm", "md", "lg"],
            search: false,
            render: (_: any, record: any) => {
                if(record?.subs_type?.id !== 4) {
                return '-';
                }
                return record?.client?.is_moral ? <Tag color="blue">{t("common.moral")}</Tag> : <Tag color="pink">{t("common.physique")}</Tag>;
            },
            valueType: "select",
            valueEnum: {
                "1": {
                    text: t("common.active"),
                    status: 'Success'
                },
                "0": {
                    text: t("common.inactive"),
                    status: 'Error'
                }
            },
        },
        {
            title: `${t("manage_newSubs.labels.periodicity")}`,
            dataIndex: "id_periodicity",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => {
                return data?.periodicity ? (
                    <span>{data.periodicity[`nom_${currentLang}`]}</span>
                ) : '-';
            },
            valueType: "select",
            fieldProps: {
                showSearch: true,
                placeholder: t("manage_newSubs.placeholders.periodicity"),
                options: periodicities?.map((el:any) => ({
                    label: el[`nom_${currentLang}`],
                    value: el.id
                })),
                filterOption: (input: string, option?: { label: string; value: number }) =>
                    option?.label.toLowerCase().includes(input.toLowerCase()) ?? false
            }
        },
        {
            title: `${t("manage_newSubs.labels.trip")}`,
            dataIndex: "id_trip",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => {
                return data?.trip ? (
                    <div className="flex gap-1">
                        <span>{data.trip[`nom_${currentLang}`]}</span>
                    </div>
                ) : '-';
            },
            search: false,
        },
        {
            title: `${t("manage_newSubs.labels.socialAffair")}`,
            dataIndex: "is_social_affair",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <Switch
                    checked={record.is_social_affair}
                    disabled
                    size="small"
                />
            ),
            valueType: "select",
            valueEnum: {
                "1": {
                    text: t("common.active"),
                    status: 'Success'
                },
                "0": {
                    text: t("common.inactive"),
                    status: 'Error'
                }
            },
        },
        {
            title: `${t("manage_newSubs.labels.stagiaire")}`,
            dataIndex: "is_stagiaire",
            responsive: ["xs", "sm", "md", "lg"],
            search: false,
            render: (_: any, record: any) => (
                <Switch
                    checked={record.is_stagiaire}
                    disabled
                    size="small"
                />
            ),
            valueType: "select",
            valueEnum: {
                "1": {
                    text: t("common.active"),
                    status: 'Success'
                },
                "0": {
                    text: t("common.inactive"),
                    status: 'Error'
                }
            },
        },
        {
            title: `${t("manage_newSubs.labels.isPrinted")}`,
            dataIndex: "is_printed",
            responsive: ["xs", "sm", "md", "lg"],
            search: false,
            render: (_: any, record: any) => (
                <Switch
                    checked={record.is_printed}
                    disabled
                    size="small"
                />
            ),
            valueType: "select",
            valueEnum: {
                "1": {
                    text: t("common.active"),
                    status: 'Success'
                },
                "0": {
                    text: t("common.inactive"),
                    status: 'Error'
                }
            },
        },
        {
            title: `${t("manage_newSubs.labels.reversed")}`,
            dataIndex: "is_reversed",
            responsive: ["xs", "sm", "md", "lg"],
            search: false,
            render: (_: any, record: any) => (
                <Switch
                    checked={record.is_reversed}
                    disabled
                    size="small"
                />
            ),
            valueType: "select",
            valueEnum: {
                "1": {
                    text: t("common.active"),
                    status: 'Success'
                },
                "0": {
                    text: t("common.inactive"),
                    status: 'Error'
                }
            },
        },
        {
            title: "Status",
            dataIndex: "status",
            render: (_: any, data: any) => {
                const status = data?.status || 'NOTPAYED';
                switch(status) {
                    case 'PAYED':
                        return <Tag color={"green"}>{t("manage_newSubs.paymentOptions.payed")}</Tag>;
                    case 'CANCELED':
                        return <Tag color={"red"}>{t("manage_newSubs.paymentOptions.canceled")}</Tag>;
                    case 'NOTPAYED':
                    default:
                        return <Tag color={"orange"}>{t("manage_newSubs.paymentOptions.notPayed")}</Tag>;
                }
            },
            valueType: "select",
            valueEnum: {
                "PAYED": {
                    text: t("manage_newSubs.paymentOptions.payed"),
                    status: 'Success'
                },
                "CANCELED": {
                    text: t("manage_newSubs.paymentOptions.canceled"),
                    status: 'Error'
                },
                "NOTPAYED": {
                    text: t("manage_newSubs.paymentOptions.notPayed"),
                    status: 'Warning'
                }
            },
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_newSubs.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            width: 100,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_newSubs.labels.actions")}`,
            fixed: "right",
            width: 220,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    {record?.status === "PAYED" && !record?.is_printed && (
                        <Tooltip title={t("manage_newSubs.tooltips.viewCard")}>
                            <Button
                                className="btn-view-card"
                                icon={<IdcardOutlined />}
                                onClick={() => handleViewCard(record)}
                            />
                        </Tooltip>
                    )}

                    {
                        hasPermission('manage_duplicata') && record?.status === 'PAYED' && record?.is_printed &&
                        <Tooltip title={t("manage_newSubs.tooltips.duplicat")}>
                                <Button
                                    className="btn-renew"
                                    icon={<Copy color="purple" width={15} />}
                                    onClick={() => handleDuplicataCard(record)}
                                />
                        </Tooltip>
                    }

                    {record?.status === "PAYED" && (
                        <Tooltip title={t("manage_newSubs.tooltips.renew")}>
                            <Button
                                className="btn-renew"
                                icon={<CalendarSync color="purple" width={15} />}
                                onClick={() => handleRenew(record)}
                            />
                        </Tooltip>
                    )}

                   {
                    <Tooltip
                        title={t("manage_newSubs.tooltips.payment")}
                    >
                        <Button
                            className="btn-view"
                            icon={<WalletMinimal color="green" width={15}/>}
                            onClick={() => handlePayment(record)}
                        />
                    </Tooltip>
                   }

                   {record?.status === "PAYED"  && (
                        <Tooltip title={t("manage_newSubs.tooltips.cancel")}>
                            <Popconfirm
                                title={t("manage_newSubs.confirmCancel")}
                                onConfirm={() => handleCancelTransaction(record)}
                                okText={t("common.yes")}
                                cancelText={t("common.no")}
                            >
                                <Button
                                    className="btn-cancel"
                                    icon={<UndoDot width={20} />}
                                />
                            </Popconfirm>
                        </Tooltip>
                    )}
                    { record?.status === 'NOTPAYED' && (
                        <Button
                            className="btn-view"
                            icon={<EyeOutlined />}
                            onClick={() => handleView(record)}
                        />
                    )}
                    {( (record?.status === 'NOTPAYED' && hasPermission('edit_newSubs')) || ((record.status === 'PAYED' || record.status === 'PENDING') && hasPermission('manage_editing_payed_subscriptions'))) &&  (
                        <Button
                            className="btn-edit"
                            icon={<EditOutlined />}
                            onClick={() => handleEdit(record)}
                        />
                    )}
                   { record?.status === 'NOTPAYED' && hasPermission("delete_newSubs") && (
                        <Popconfirm
                            title={t("manage_newSubs.confirmDelete")}
                            onConfirm={() => handleDelete(record.id)}
                            okText={t("manage_newSubs.yes")}
                            cancelText={t("manage_newSubs.no")}
                        >
                            <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                        </Popconfirm>
                    )}
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.dashboard")}</Link>,
        },
        {
            title: t("manage_newSubs.title"),
        },
    ];


    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = async (record: any) => {
        setSelectedClient(record.client);
        setSelectedAbnType(record.subs_type);
        setEditingNewSubs(record);
        setIsReversed(record.is_reversed === true || record.is_reversed === 1);
        setIsSocialAffair(record.is_social_affair === true || record.is_social_affair === 1);
        setViewMode(true);
        setModalVisible(true);
        setLoading(true);

        const stationStartId = record.trip?.id_station_start || null;
        const stationEndId = record.trip?.id_station_end || null;

        const departureStationId = record.is_reversed ? stationEndId : stationStartId;
        const arrivalStationId = record.is_reversed ? stationStartId : stationEndId;

        if (record?.client?.governorate) {
            await handleGovernorateChange(record.client?.governorate?.id);
        }

        if (record.subs_type && !record.subs_type.is_student && !record.subs_type.is_impersonal && !record.subs_type.is_conventional) {
            setSpecialClient(record.special_client);
            form.setFieldsValue({ special_client: record.special_client });
        } else {
            setSpecialClient(null);
            form.setFieldsValue({ special_client: null });
        }

        form.setFieldsValue({
            // client data
            firstname: record?.client?.firstname,
            lastname: record?.client?.lastname,
            identity_number: record?.client?.identity_number,
            phone: record?.client?.phone,
            address: record?.client?.address,
            dob: record?.client?.dob ? moment(record?.client?.dob) : null,
            id_governorate: record?.client?.governorate?.id,
            id_delegation: record?.client?.delegation?.id,
            id_degree: record?.client?.degree?.id,
            id_establishment: record?.client?.establishment?.id,
            is_moral: record?.client?.is_moral,
            is_withTVA: record?.client?.is_withTVA,

            // subscription data
            is_stagiaire: record?.is_stagiaire,
            stage_date_start: record?.stage_date_start ? moment(record?.stage_date_start) : null,
            stage_date_end: record?.stage_date_end ? moment(record?.stage_date_end) : null,
            id_subs_type: record?.subs_type?.id,
            id_periodicity: record?.periodicity?.id,
            id_trip: record?.id_trip,
            station_depart: departureStationId,
            station_arrival: arrivalStationId,
            id_line: record.trip?.id_line,
            rest_days: Array.isArray(record.rest_days) ? record.rest_days.map(Number) : [],
            hasVacances: record?.hasVacances,
            is_social_affair: record.is_social_affair === true || record.is_social_affair === 1,
            status: record.status || 'NOTPAYED',
            photo: record.photo || undefined,
            subs_number: record?.subs_number,
        });

        if(record?.subs_type?.is_student && !record?.subs_type?.hasCIN){
            form.setFieldsValue({
                identity_number: record.client.identity_number || record.client.matricule,
                dob: record.client.dob ? moment(record.client.dob) : null
            });
        }

        setSelectedStationDepart(departureStationId);
        setSelectedStationArrival(arrivalStationId);

        const lineId = record.trip?.id_line || null;
        setSelectedLine(lineId);

        const availablesLines = await fetchLinesByStations(departureStationId, arrivalStationId, record.subs_type.id);
        setAvailableLines(availablesLines)

        const fetchData = async () => {
            try {
                if (record.subs_type) {
                    const subsType = record.subs_type;
                    await fetchStations(subsType.id, record.client?.delegation?.id);
                }
                if (departureStationId && record.subs_type?.id) {
                    const id_delegation_establishment = record?.client?.establishment?.id_delegation
                    const connectedStationsResult = await fetchConnectedStations(departureStationId, record.subs_type.id, id_delegation_establishment);

                    // Make sure the station_arrival is set in the form after connected stations are loaded
                    if (arrivalStationId && connectedStationsResult && connectedStationsResult.length > 0) {
                        setSelectedStationArrival(arrivalStationId);
                        form.setFieldsValue({ station_arrival: arrivalStationId });
                    }

                    if (lineId) {
                        await handleLineChange(lineId);
                    }
                    else if (arrivalStationId) {
                        await fetchLinesByStations(departureStationId, arrivalStationId, record.subs_type.id);
                    }
                }
            } catch (error) {
                console.error('Error loading data:', error);
                toast.error(t("common.errors.unexpected"));
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    };

    const handleEdit = async (record: any) => {
        setViewMode(false);
        setIsRenewal(false)
        setModalVisible(true);
        setLoading(true);
        setSelectedClient(record.client);
        setSelectedAbnType(record.subs_type);
        setIsSocialAffairVerified(false);
        setIsSocialAffair(false);
        setEditingNewSubs(record);
        setIsReversed(record.is_reversed === true || record.is_reversed === 1);
        setIsSocialAffair(record.is_social_affair === true || record.is_social_affair === 1);
        setIsStagiaire(record?.is_stagiaire === true || record?.is_stagiaire === 1);
        setIsImpersonalMoral(record?.client?.is_moral === true || record?.client?.is_moral === 1);


         if (record.status === 'PAYED' && hasPermission('manage_editing_payed_subscriptions')) {
            toast.warning(t("messages.paidSubscriptionEditWarning"),
                { autoClose: 20000 });
        }

        const stationStartId = record.trip?.id_station_start || null;
        const stationEndId = record.trip?.id_station_end || null;

        const departureStationId = record.is_reversed ? stationEndId : stationStartId;
        const arrivalStationId = record.is_reversed ? stationStartId : stationEndId;

        if (record?.client?.governorate) {
            await handleGovernorateChange(record.client?.governorate?.id);
        }

        if (record.subs_type && !record.subs_type.is_student && !record.subs_type.is_impersonal && !record.subs_type.is_conventional) {
            setSpecialClient(record.special_client);
            form.setFieldsValue({ special_client: record.special_client });
        } else {
            setSpecialClient(null);
            form.setFieldsValue({ special_client: null });
        }

        form.setFieldsValue({
            // client data
            firstname: record?.client?.firstname,
            lastname: record?.client?.lastname,
            identity_number: record?.client?.identity_number,
            phone: record?.client?.phone,
            address: record?.client?.address,
            dob: record?.client?.dob ? moment(record?.client?.dob) : null,
            id_governorate: record?.client?.governorate?.id,
            id_delegation: record?.client?.delegation?.id,
            id_degree: record?.client?.degree?.id,
            id_establishment: record?.client?.establishment?.id,
            society_name: record?.client?.society_name,
            legal_representative: record?.client?.legal_representative,
            is_moral: record.client?.is_moral === true || record.client?.is_moral === 1,
            is_withTVA: record.client?.is_withTVA === true || record.client?.is_withTVA === 1,

            // subscription data
            is_stagiaire: record?.is_stagiaire,
            stage_date_start: record?.stage_date_start ? moment(record?.stage_date_start) : null,
            stage_date_end: record?.stage_date_end ? moment(record?.stage_date_end) : null,
            id_subs_type: record?.subs_type?.id,
            id_periodicity: record?.periodicity?.id,
            id_trip: record?.id_trip,
            station_depart: departureStationId,
            station_arrival: arrivalStationId,
            id_line: record.trip?.id_line,
            rest_days: Array.isArray(record.rest_days) ? record.rest_days.map(Number) : [],
            hasVacances: record?.hasVacances,
            is_social_affair: record.is_social_affair === true || record.is_social_affair === 1,
            status: record.status || 'NOTPAYED',
            photo: record.photo || undefined,
            subs_number: record?.subs_number,
        });

        if(record?.subs_type?.is_student && !record?.subs_type?.hasCIN){
            form.setFieldsValue({
                identity_number: record.client.identity_number || record.client.matricule,
                dob: record.client.dob ? moment(record.client.dob) : null
            });
        }

        if (record?.client?.dob) {
            setDobSelected(true);
            const age = calculateAge(record.client.dob);
            const filtered = schoolDegrees.filter((deg: any) => age <= deg?.age_max);
            setFilteredDegrees(filtered);
        }

        setSelectedStationDepart(departureStationId);
        setSelectedStationArrival(arrivalStationId);

        const lineId = record.trip?.id_line || null;
        setSelectedLine(lineId);

        const availablesLines = await fetchLinesByStations(departureStationId, arrivalStationId, record.subs_type.id);
        setAvailableLines(availablesLines)

        const fetchData = async () => {
            try {
                if (record.subs_type) {
                    const subsType = record.subs_type;
                    await fetchStations(subsType.id);
                }

                if (departureStationId && record.subs_type?.id) {
                    const id_delegation_establishment = record?.client?.establishment?.id_delegation
                    const connectedStationsResult = await fetchConnectedStations(departureStationId, record.subs_type.id, id_delegation_establishment);

                    if (arrivalStationId && connectedStationsResult && connectedStationsResult.length > 0) {
                        setSelectedStationArrival(arrivalStationId);
                        form.setFieldsValue({ station_arrival: arrivalStationId });
                    }

                    if (lineId) {
                        await handleLineChange(lineId);
                    }
                    else if (arrivalStationId) {
                        await fetchLinesByStations(departureStationId, arrivalStationId, record.subs_type.id);
                    }
                }
            } catch (error) {
                console.error('Error loading data:', error);
                toast.error(t("common.errors.unexpected"));
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    };


    const handlePayment = (record: any) => {
        setAbnRecord(record);
        setEditingNewSubs(record);
        setViewMode(true);

        const isRenewalRecord = record.id_parent;
        setIsRenewal(!!isRenewalRecord);

        setPaymentModal(true);
    };

    const handleAdd = () => {
        setEditingNewSubs(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
        setStations([]);
        setConnectedStations([]);
        setAvailableLines([]);
        setLineDetails(null);
        setIsReversed(false);
        setIsSocialAffairVerified(false);
        setIsSocialAffair(false);
    };

    const handleDuplicataCard:any = (record:any) => {
        setIsDuplicated(true)
        setAbnRecord(record);
        setIsCardModalVisible(true);
    };

    const handleViewCard:any = (record:any) => {
        setAbnRecord(record);
        setIsCardModalVisible(true);
    };

    const handleRenew = async (record: any) => {
        setViewMode(false);
        setIsRenewal(true);
        setModalVisible(true);
        setLoading(true);
        setIsSocialAffairVerified(false);
        setIsSocialAffair(false);

        setSelectedClient(record.client);
        setSelectedAbnType(record.subs_type);
        setEditingNewSubs(record);

        setIsReversed(record.is_reversed === true || record.is_reversed === 1);

        const stationStartId = record.trip?.id_station_start || null;
        const stationEndId = record.trip?.id_station_end || null;

        const departureStationId = record.is_reversed ? stationEndId : stationStartId;
        const arrivalStationId = record.is_reversed ? stationStartId : stationEndId;;

        if (record?.client?.governorate) {
            await handleGovernorateChange(record.client?.governorate?.id);
        }


        if (record.subs_type && !record.subs_type.is_student && !record.subs_type.is_impersonal && !record.subs_type.is_conventional) {
            setSpecialClient(record.special_client);
            form.setFieldsValue({ special_client: record.special_client });
        } else {
            setSpecialClient(null);
            form.setFieldsValue({ special_client: null });
        }
        form.setFieldsValue({
            // client data
            firstname: record?.client?.firstname,
            lastname: record?.client?.lastname,
            identity_number: record?.client?.identity_number,
            phone: record?.client?.phone,
            address: record?.client?.address,
            dob: record?.client?.dob ? moment(record?.client?.dob) : null,
            id_governorate: record?.client?.governorate?.id,
            id_delegation: record?.client?.delegation?.id,
            id_degree: record?.client?.degree?.id,
            id_establishment: record?.client?.establishment?.id,
            is_moral: record?.client?.is_moral,
            is_withTVA: record?.client?.is_withTVA,

            // subscription data
            id_subs_type: record?.subs_type?.id,
            id_periodicity: record?.periodicity?.id,
            id_trip: record?.id_trip,
            station_depart: departureStationId,
            station_arrival: arrivalStationId,
            id_line: record.trip?.id_line,
            rest_days: Array.isArray(record.rest_days) ? record.rest_days.map(Number) : [],
            hasVacances: record?.hasVacances,
            is_social_affair: false,
            is_stagiaire: false,
            status: record.status || 'NOTPAYED',
            photo: record.photo || undefined,
            subs_number: record?.subs_number,
        });

        setSelectedStationDepart(departureStationId);
        setSelectedStationArrival(arrivalStationId);

        const lineId = record.trip?.id_line || null;
        setSelectedLine(lineId);

        const availablesLines = await fetchLinesByStations(departureStationId, arrivalStationId, record.subs_type.id);
        setAvailableLines(availablesLines)

        const fetchData = async () => {
            try {
                if (record.subs_type) {
                    const subsType = record.subs_type;
                    await fetchStations(subsType.id);
                }
                if (departureStationId && record.subs_type?.id) {
                    const id_delegation_establishment = record?.client?.establishment?.id_delegation
                    // Fetch connected stations and wait for the result
                    const connectedStationsResult = await fetchConnectedStations(departureStationId, record.subs_type.id, id_delegation_establishment);

                    // Make sure the station_arrival is set in the form after connected stations are loaded
                    if (arrivalStationId && connectedStationsResult && connectedStationsResult.length > 0) {
                        setSelectedStationArrival(arrivalStationId);
                        form.setFieldsValue({ station_arrival: arrivalStationId });
                    }

                    if (lineId) {
                        await handleLineChange(lineId);
                    }
                    else if (arrivalStationId) {
                        await fetchLinesByStations(departureStationId, arrivalStationId, record.subs_type.id);
                    }
                }
            } catch (error) {
                console.error('Error loading data:', error);
                toast.error(t("common.errors.unexpected"));
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE SUBS IMAGE CROP
    |-------------------------------------------------------------------------- */
    const handleUpload = (file: File) => {
        const reader = new FileReader();
        reader.addEventListener('load', () => {
            if(reader.result) {
                setImageSrc(reader.result.toString());
                setCropModalVisible(true);
            }
        });
        reader.readAsDataURL(file);
        return false;
    };

    const updateIsReversedBasedOnStations = (lineData: any, departureStationId: number, arrivalStationId: number) => {
        if (!lineData || !lineData.stations || !departureStationId || !arrivalStationId) {
            return;
        }

        const departureStationIndex = lineData.stations.findIndex((station: any) => station.id === departureStationId);
        const arrivalStationIndex = lineData.stations.findIndex((station: any) => station.id === arrivalStationId);

        if (departureStationIndex !== -1 && arrivalStationIndex !== -1) {
            const isReversed = departureStationIndex > arrivalStationIndex;
            setIsReversed(isReversed);
            return isReversed;
        }
        return null;
    };

    const handleLineChange = async (lineId: number) => {
        setSelectedLine(lineId);
        form.setFieldsValue({ id_line: lineId });

        if (!lineId) {
            setLineDetails(null);
            return null;
        }

        try {
            setIsLoadingLineDetails(true);

            const selectedLineData = availableLines.find(line => line.id === lineId);

            if (lineDetails?.id === lineId && lineDetails.stations && lineDetails.stations.length > 0) {
                if (selectedStationDepart && selectedStationArrival) {
                    updateIsReversedBasedOnStations(lineDetails, selectedStationDepart, selectedStationArrival);
                }
                return lineDetails;
            }

            const response = await dispatch(getLineStationsAndRoutes(lineId)).unwrap();

            let lineData = null;

            if (response && response.data) {
                lineData = response.data;
            } else if (response) {
                lineData = response;
            }

            if (lineData) {
                if (selectedLineData) {
                    if (!lineData.name && !lineData[`nom_${currentLang}`]) {
                        lineData.name = selectedLineData.name || selectedLineData[`nom_${currentLang}`];
                        lineData[`nom_${currentLang}`] = selectedLineData[`nom_${currentLang}`] || selectedLineData.name;
                    }
                }

                if (lineData.stations && lineData.stations.length > 0 && selectedStationDepart && selectedStationArrival) {
                    updateIsReversedBasedOnStations(lineData, selectedStationDepart, selectedStationArrival);
                }
                setLineDetails(lineData);
            } else {
                setLineDetails(null);
            }

            return lineData;
        } catch (error) {
            console.error('Error fetching line details:', error);
            toast.error(t("common.errors.unexpected"));
            setLineDetails(null);
            return null;
        } finally {
            setIsLoadingLineDetails(false);
        }
    };

    const calculateAge = (dob: string | dayjs.Dayjs): number => {
        return dayjs().diff(dayjs(dob), 'year');
    };
    const handleDobChange = (date: dayjs.Dayjs | null) => {
        if (date && dayjs(date).isValid()) {
          setDobSelected(true);
          const age = calculateAge(date);

          console.log(age);

          const filtered = schoolDegrees.filter((deg: any) => age <= deg?.age_max);
          console.log(filtered);

          setFilteredDegrees(filtered);
        } else {
          setDobSelected(false);
          setFilteredDegrees([]);
          form.setFieldsValue({ id_degree: undefined });
        }
      };



    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE SUBS
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });

        const photoValue = values.photo;

        // Prepare client data
        const clientData = {
            firstname: values.firstname,
            lastname: values.lastname,
            identity_number: values.identity_number,
            phone: values.phone,
            address: values.address,
            dob: values.dob ? dayjs(values.dob).format('YYYY-MM-DD') : null,
            id_governorate: values.id_governorate || null,
            id_delegation: values.id_delegation || null,
            id_degree: values.id_degree || null,
            id_establishment: values?.id_establishment || null,

            is_moral: values?.is_moral === true ? 1 : 0,
            is_withTVA: isImpersonalMoral ? values?.is_withTVA === true ? 1 : 0 : null,

            society_name: values?.society_name || null,
            legal_representative: values?.legal_representative || null,

            // For subscribers to authenticate
            email: values.email,
            password: values.password || 'client'
        };

        const subscriptionData = {
            is_stagiaire: values?.is_stagiaire === true ? 1 : 0,
            stage_date_start: values.stage_date_start ? dayjs(values.stage_date_start).format('YYYY-MM-DD') : null,
            stage_date_end: values.stage_date_end ? dayjs(values.stage_date_end).format('YYYY-MM-DD') : null,
            id_subs_type: values.id_subs_type || selectedAbnType?.id,
            id_periodicity: values.id_periodicity,
            id_station_start: isReversed === true ? selectedStationArrival : selectedStationDepart,
            id_station_end: isReversed === true ? selectedStationDepart : selectedStationArrival,
            id_line: selectedLine,
            is_reversed: isReversed === true ? 1 : 0,
            photo: photoValue || croppedImage || editingNewSubs?.photo,
            is_social_affair: isSocialAffair ? 1 : 0,
            status: values.status || editingNewSubs?.status || 'NOTPAYED',
            hasVacances: values.hasVacances === true ? 1 : 0,
            id_trip: editingNewSubs?.id_trip,
            rest_days: values.rest_days || [],
            subs_number: values.subs_number,
            special_client: values.special_client
        };

        let payload;

        if (isRenewal) {
            payload = {
                ...subscriptionData,
                ...clientData,
                id_parent: editingNewSubs?.id,
                renewal_date: dayjs( new Date() ).format('YYYY-MM-DD'),
                status: 'NOTPAYED'
            };
        } else if (editingNewSubs) {
            payload = {
                id: editingNewSubs.id,
                ...subscriptionData,
                ...clientData
            };
        } else {
            payload = {
                ...subscriptionData,
                ...clientData
            };
        }

        try {
            if (isRenewal) {
                await dispatch(storeSubscription(payload)).unwrap();
                toast.update(toastId, {
                    render: t("messages.success"),
                    type: "success",
                    isLoading: false,
                    autoClose: 3000
                });
            } else if (editingNewSubs) {
                await dispatch(updateSubscription(payload)).unwrap();
                toast.update(toastId, {
                    render: t("messages.success"),
                    type: "success",
                    isLoading: false,
                    autoClose: 3000
                });
            } else {
                await dispatch(storeClientWithSubscription(payload)).unwrap();

                toast.update(toastId, {
                    render: t("messages.success"),
                    type: "success",
                    isLoading: false,
                    autoClose: 3000
                });
            }
            setIsRenewal(false);
            actionRef.current?.reload();
            handleReset()
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: error || error.message,
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    const confirmSubmit = (values: any) => {
        if (values.is_social_affair && !isSocialAffair) {
            toast.error(t("social_affairs.verification_required"), {
                position: 'top-center',
                autoClose: 5000
            });
            return;
        }
        const modal = Modal.confirm({
            title: t("manage_newSubs.confirmAction"),
            content: isRenewal
                ? t("manage_newSubs.confirmRenewal")
                : editingNewSubs
                ? t("manage_newSubs.confirmUpdate")
                : t("manage_newSubs.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE SUBS
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteSubscription(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - CANCEL PAYED TRANSACTION
    |-------------------------------------------------------------------------- */
    const handleCancelTransaction = async (record: any) => {
        const toastId = toast.loading(t("manage_newSubs.cancelingTransaction"), {
            position: 'top-center',
        });
        try {
            await dispatch(cancelTransaction(record.id)).unwrap();
            toast.update(toastId, {
                render: t("manage_newSubs.transactionCanceled"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: error?.message || t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false);
        form.resetFields();
        setIsRenewal(false)
        setSelectedClient(null);
        setSelectedStationDepart(null);
        setSelectedStationArrival(null);
        setSelectedLine(null);
        setCroppedImage(null);
        setPaymentModal(false);
        setIsSocialAffair(false);
        setSelectedAbnType(null);
        setStations([]);
        setConnectedStations([]);
        setAvailableLines([]);
        setLineDetails(null);
        setIsReversed(false);
        setIsOpenSellsPeriod(false)
        setFilteredDelegations([]);
        setSelectedFilterGovernorate(null);
        setAvailableLines([]);
    }

    /*|--------------------------------------------------------------------------
    |  - EXPORT FUNCTIONALITY
    |-------------------------------------------------------------------------- */
    const getSubscriptionsForExport = async (): Promise<any[]> => {
        try {
            const response = await dispatch(getSubscriptions({
                pageNumber: 1,
                perPage: 50000,
                params: lastUsedParams,
                sort: lastUsedSort,
                filter: lastUsedFilter
            })).unwrap();

            return response.data || [];
        } catch (error) {
            console.error('Erreur lors de la récupération des abonnements pour export:', error);
            throw error;
        }
    };

    const handleExportToExcel = async () => {
        setExportLoading(true);
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            const subscriptionsData = await getSubscriptionsForExport();

            if (subscriptionsData.length === 0) {
                toast.update(toastId, {
                    render: t("manage_stats.no_data_to_export"),
                    type: "warning",
                    isLoading: false,
                    autoClose: 3000
                });
                return;
            }

            const exportColumns: ExportColumn[] = [
                {
                    title: t("manage_newSubs.labels.client"),
                    dataIndex: "client",
                    key: "client",
                    render: (_, record: any) =>
                        record?.client?.is_moral ? record.client.society_name : `${record.client.firstname} ${record.client.lastname}`
                },
                {
                    title: t("manage_newSubs.labels.abnType"),
                    dataIndex: ["subs_type", `nom_${currentLang}`],
                    key: "subs_type",
                    render: (_, record: any) => record?.subs_type?.[`nom_${currentLang}`] || '-'
                },
                {
                    title: t("manage_newSubs.labels.periodicity"),
                    dataIndex: ["periodicity", `nom_${currentLang}`],
                    key: "periodicity",
                    render: (_, record: any) => record?.periodicity?.[`nom_${currentLang}`] || '-'
                },
                {
                    title: t("manage_newSubs.labels.trip"),
                    dataIndex: ["trip", `nom_${currentLang}`],
                    key: "trip",
                    render: (_, record: any) => record?.trip?.[`nom_${currentLang}`] || '-'
                },
                {
                    title: t("manage_newSubs.labels.socialAffair"),
                    dataIndex: "is_social_affair",
                    key: "is_social_affair",
                    render: (value: any) => value ? t("common.yes") : t("common.no")
                },
                {
                    title: t("manage_newSubs.labels.reversed"),
                    dataIndex: "is_reversed",
                    key: "is_reversed",
                    render: (value: any) => value ? t("common.yes") : t("common.no")
                },
                {
                    title: "Status",
                    dataIndex: "status",
                    key: "status",
                    render: (value: any) => {
                        switch(value) {
                            case 'PAYED':
                                return t("manage_newSubs.paymentOptions.payed");
                            case 'CANCELED':
                                return t("manage_newSubs.paymentOptions.canceled");
                            case 'NOTPAYED':
                            default:
                                return t("manage_newSubs.paymentOptions.notPayed");
                        }
                    }
                },
                {
                    title: t("manage_newSubs.labels.createdAt"),
                    dataIndex: "created_at",
                    key: "created_at",
                    render: (value: any) => value ? dayjs(value).format('YYYY-MM-DD') : '-'
                },
                {
                    title: t("manage_users.labels.phone"),
                    dataIndex: ["client", "phone"],
                    key: "client_phone",
                    render: (_, record: any) => record?.client?.phone || '-'
                },
                {
                    title: t("manage_users.labels.cin"),
                    dataIndex: ["client", "identity_number"],
                    key: "client_identity",
                    render: (_, record: any) => record?.client?.identity_number || '-'
                },
                {
                    title: t("manage_users.labels.governorate"),
                    dataIndex: ["client", "governorate"],
                    key: "client_governorate",
                    render: (_, record: any) => record?.client?.governorate?.[`nom_${currentLang}`] || '-'
                }
            ];

            const filename = currentLang === 'fr' ? 'abonnements' :
                           currentLang === 'en' ? 'subscriptions' :
                           'الاشتراكات';

            exportToExcel({
                filename,
                sheetName: t("manage_newSubs.title"),
                columns: exportColumns,
                data: subscriptionsData,
                currentLang
            });

            toast.update(toastId, {
                render: t("manage_stats.export_success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
        } catch (error) {
            toast.update(toastId, {
                render: t("manage_stats.export_error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setExportLoading(false);
        }
    };

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_newSubs.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        request={handleGetSubscriptions}
                        pagination={{
                            pageSize,
                            total,
                            current: pageNumber,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        scroll={{ x: 800 }}
                        loading={loadingFetch}
                        toolBarRender={() => [
                            <Button
                                key="export"
                                onClick={handleExportToExcel}
                                loading={exportLoading}
                                icon={<DownloadOutlined />}
                                className="border-green-500 !text-gray-600 hover:bg-gray-50 hover:border-gray-600"
                            >
                                {exportLoading ? t("manage_stats.export_loading") : t("manage_stats.export_excel")}
                            </Button>,
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_newSubs.add")}
                            </Button>,
                        ]}
                        search={{
                        labelWidth: "auto",
                        className: "bg-[#FAFAFA]",
                        defaultCollapsed: false,
                        optionRender: (_, __, dom) => [
                            ...dom.reverse(),
                        ],
                        layout: "vertical",
                        span: {
                            xs: 24,
                            sm: 12,
                            md: 8,
                            lg: 8,
                            xl: 8,
                            xxl: 8,
                        },
                    }}
                    />
                </Col>
            </Row>

            <Modal
                width={1100}
                title={
                    viewMode
                        ? t("manage_newSubs.details")
                        : isRenewal
                        ? t("manage_newSubs.renewal")
                        : editingNewSubs
                            ? t("manage_newSubs.edit")
                            : t("manage_newSubs.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_newSubs.save")}
                footer={viewMode ? null : undefined}
            >
                <Spin spinning={loading}>
                    <Form className="form-inputs" form={form} layout="vertical" onFinish={confirmSubmit}>
                        <Row gutter={16}>
                            <Col xs={24} sm={24}>
                                <Form.Item
                                    name="id_subs_type"
                                    initialValue={null}
                                    rules={[{
                                        required: true,
                                        message: t("manage_newSubs.errors.abnTypeRequired")
                                    }]}
                                >
                                    <div className="flex flex-col items-center w-full max-w-5xl mx-auto bg-gray-50/50 rounded-2xl p-6">
                                        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 w-full">
                                            {loadingFetch ? (
                                                <div className="col-span-full flex justify-center py-8">
                                                    <Spin />
                                                </div>
                                            ) : (
                                                abnTypes && abnTypes.map((item: any) => {
                                                    const isSelected = selectedAbnType?.id === item.id;

                                                    const noPermissionImpersonal = item.is_impersonal && !hasPermission('manage_abn_impersonnels');
                                                    const noPermissionConventional = item.is_conventional && !hasPermission('manage_abn_convention_impersonnels');

                                                    //   Skip rendering this item if the user has no permission
                                                    if (noPermissionImpersonal || noPermissionConventional) {
                                                        return null;
                                                    }

                                                    return (
                                                        <div
                                                            key={item.id}
                                                            onClick={() => {
                                                                if (!viewMode && !editingNewSubs && !isRenewal) {
                                                                    handleAbnTypeChange(item.id);
                                                                }
                                                            }}
                                                            className={`
                                                                relative flex flex-col items-center
                                                                p-4 rounded-xl cursor-pointer
                                                                transition-all duration-300 ease-in-out
                                                                ${isSelected
                                                                    ? 'bg-white ring-2 ring-red-500 shadow-lg transform scale-[1.02]'
                                                                    : 'bg-white hover:bg-red-50/30 border border-gray-100 hover:border-red-200 hover:shadow'}
                                                                ${viewMode ? 'opacity-75 cursor-not-allowed' : ''}
                                                            `}
                                                        >
                                                            {isSelected && (
                                                                <div className="absolute -top-2 -right-2 bg-red-500 rounded-full p-1.5 shadow-sm">
                                                                    <CheckCircle className="text-white w-3 h-3" />
                                                                </div>
                                                            )}

                                                            <div className={`
                                                                p-2.5 rounded-full mb-3
                                                                ${isSelected
                                                                    ? 'bg-red-100 text-red-600'
                                                                    : 'bg-gray-100/80 text-gray-600'}
                                                            `}>
                                                                <Ticket className="w-5 h-5" />
                                                            </div>

                                                            <h3 className={`
                                                                text-sm font-medium text-center mb-1
                                                                line-clamp-2 leading-tight
                                                                ${isSelected ? 'text-red-700' : 'text-gray-700'}
                                                            `}>
                                                                {item[`nom_${currentLang}`]}
                                                            </h3>

                                                            <p className={`
                                                                text-[11px] text-center
                                                                line-clamp-2 leading-snug
                                                                ${isSelected ? 'text-red-500/80' : 'text-gray-500'}
                                                            `}>
                                                                {item[`description_${currentLang}`]}
                                                            </p>
                                                        </div>
                                                    );
                                                })

                                            )}
                                        </div>
                                    </div>
                                </Form.Item>

                                {
                                    isOpenSellsPeriod ? (
                                        <SellsPeriodClosedInfo isOpen={true} />
                                    ) : (
                                        <>
                                            <div className="flex justify-end">
                                                {selectedAbnType && !selectedAbnType?.is_impersonal && !selectedAbnType?.is_conventional && !loading &&  (
                                                    <Form.Item
                                                        label={t("manage_newSubs.labels.isStagiaire")}
                                                        name="is_stagiaire"
                                                        valuePropName="checked"
                                                        className="mb-0"
                                                    >
                                                        <Switch
                                                            disabled={viewMode}
                                                            onChange={(checked) => setIsStagiaire(checked)}
                                                        />
                                                    </Form.Item>
                                                )}
                                            </div>

                                            {isStagiaire && selectedAbnType && !loading && (
                                                <Row gutter={16} className="mt-4">
                                                    <Col span={12}>
                                                        <Form.Item
                                                            label={t("manage_newSubs.labels.stageStartDate")}
                                                            name="stage_date_start"
                                                            rules={[{ required: true, message: t("manage_newSubs.errors.stageStartDateRequired") }]}
                                                        >
                                                            <DatePicker
                                                                className="w-full"
                                                                format="YYYY-MM-DD"
                                                                disabled={viewMode}
                                                                placeholder={t("manage_newSubs.placeholders.stageStartDate")}
                                                            />
                                                        </Form.Item>
                                                    </Col>
                                                    <Col span={12}>
                                                        <Form.Item
                                                            label={t("manage_newSubs.labels.stageEndDate")}
                                                            name="stage_date_end"
                                                            rules={[{ required: true, message: t("manage_newSubs.errors.stageEndDateRequired") }]}
                                                        >
                                                            <DatePicker
                                                                className="w-full"
                                                                format="YYYY-MM-DD"
                                                                disabled={viewMode}
                                                                placeholder={t("manage_newSubs.placeholders.stageEndDate")}
                                                            />
                                                        </Form.Item>
                                                    </Col>
                                                </Row>
                                            )}
                                            {!selectedAbnType && !loadingFetch && (
                                                <div className="flex flex-col items-center text-gray-500 py-4">
                                                    <InfoIcon className="mb-2 text-red-500 animate-pulse"/>
                                                    {t("manage_newSubs.selectSubscriptionPrompt")}
                                                </div>
                                            )}

                                            {/**
                                             * ---------------------------------------------------------------
                                             *  Client form
                                             * ---------------------------------------------------------------
                                             */}
                                            {
                                                selectedAbnType && !loading && (
                                                    <Divider orientation="left">{t("manage_newSubs.clientInfo")}</Divider>
                                                )
                                            }

                                            {selectedAbnType && selectedAbnType?.is_impersonal && !loading && (
                                                <Row gutter={16} className="flex items-center justify-center">
                                                    <Col className="py-4">
                                                        <Form.Item
                                                            name="is_moral"
                                                            initialValue={false}
                                                        >
                                                            <Radio.Group
                                                                disabled={viewMode || isRenewal}
                                                                onChange={(e) => setIsImpersonalMoral(e.target.value)}
                                                                className="flex space-x-8"
                                                            >
                                                                <Radio value={false} className="rounded-full">
                                                                    <span className="ml-1">{t("manage_newSubs.labels.clientPhysique")}</span>
                                                                </Radio>
                                                                <Radio value={true} className="rounded-full">
                                                                    <span className="ml-1">{t("manage_newSubs.labels.clientMoral")}</span>
                                                                </Radio>
                                                            </Radio.Group>
                                                        </Form.Item>
                                                    </Col>
                                                </Row>
                                            )}

                                            {selectedAbnType && selectedAbnType?.is_impersonal && !loading &&  (
                                                <Row gutter={16}>

                                                </Row>
                                            )}

                                            {
                                                selectedAbnType && !selectedAbnType?.is_student && !selectedAbnType?.is_impersonal && !selectedAbnType?.is_conventional && !loading && (
                                                    <FormItem
                                                        name="special_client"
                                                        label={t("manage_newSubs.labels.specialClient")}
                                                        rules={[{ required: true, message: t("manage_newSubs.errors.specialClientRequired") }]}
                                                    >
                                                        <Select
                                                            disabled={viewMode || isRenewal}
                                                            placeholder={t("manage_newSubs.placeholders.specialClient")}
                                                            onChange={(value) => {
                                                                setSpecialClient(value);
                                                            }}
                                                        >
                                                            <Select.Option value={"CIVIL"}>{t("manage_newSubs.labels.civil")}</Select.Option>
                                                            <Select.Option value={"UNIVERSITAIRE"}>{t("manage_newSubs.labels.universitaire")}</Select.Option>
                                                            <Select.Option value={"SCOLAIRE"}>{t("manage_newSubs.labels.scolaire")}</Select.Option>
                                                        </Select>
                                                    </FormItem>
                                                )
                                            }

                                            {selectedAbnType && !loading && (
                                            <>
                                                {
                                                    selectedAbnType?.is_impersonal && isImpersonalMoral ? (
                                                        <Row gutter={16}>
                                                            <Col span={12}>
                                                                <Form.Item
                                                                    label={t("manage_users.labels.societyName")}
                                                                    name="society_name"
                                                                    rules={[{ required: true, message: t("manage_users.errors.societyNameRequired") }]}
                                                                >
                                                                    <Input
                                                                        placeholder={t("manage_users.client.placeholders.societyName")}
                                                                        disabled={viewMode || isRenewal}
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                            <Col span={12}>
                                                                <Form.Item
                                                                    label={t("manage_users.labels.legalRepresentative")}
                                                                    name="legal_representative"
                                                                    rules={[{ required: true, message: t("manage_users.errors.legalRepresentativeRequired") }]}
                                                                >
                                                                    <Input
                                                                        placeholder={t("manage_users.client.placeholders.legalRepresentative")}
                                                                        disabled={viewMode || isRenewal}
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                        </Row>
                                                    ) : (
                                                        <Row gutter={16}>
                                                            <Col span={12}>
                                                                <Form.Item
                                                                    label={t("manage_users.labels.lastname")}
                                                                    name="lastname"
                                                                    rules={[{ required: true, message: t("manage_users.errors.lastnameRequired") }]}
                                                                >
                                                                    <Input
                                                                        placeholder={t("manage_users.client.placeholders.lastname")}
                                                                        disabled={viewMode || isRenewal}
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                            <Col span={12}>
                                                                <Form.Item
                                                                    label={t("manage_users.labels.firstname")}
                                                                    name="firstname"
                                                                    rules={[{ required: true, message: t("manage_users.errors.firstnameRequired") }]}
                                                                >
                                                                    <Input
                                                                        placeholder={t("manage_users.client.placeholders.firstname")}
                                                                        disabled={viewMode || isRenewal}
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                        </Row>
                                                    )
                                                }

                                                <Row gutter={16}>
                                                    <Col span={12}>
                                                        <Form.Item
                                                            label={
                                                                    selectedAbnType && !selectedAbnType?.hasCIN && selectedAbnType?.is_student || specialClient === "SCOLAIRE"
                                                                    ? t("manage_users.labels.matricule")
                                                                    : selectedAbnType && selectedAbnType?.hasCIN
                                                                    ? t("manage_users.labels.cin")
                                                                    : selectedAbnType && selectedAbnType?.is_impersonal && !isImpersonalMoral
                                                                    ? t("manage_users.labels.cin")
                                                                    : selectedAbnType && selectedAbnType?.is_impersonal && isImpersonalMoral
                                                                    ? t("manage_users.labels.idImpersonal")
                                                                    : selectedAbnType && selectedAbnType?.is_conventional
                                                                    ? t("manage_users.labels.idConventional")
                                                                    : null
                                                            }
                                                            name="identity_number"
                                                            rules={[
                                                                { required: true, message: t("manage_users.errors.cinRequired") },
                                                                {
                                                                    pattern: /^[A-Z0-9]+$/,
                                                                    message:
                                                                        t("manage_users.errors.cinInvalid")
                                                                },
                                                            ]}
                                                        >
                                                            <Input placeholder={
                                                                selectedAbnType && !selectedAbnType?.hasCIN && selectedAbnType?.is_student || specialClient === "SCOLAIRE"
                                                                ? t("manage_users.client.placeholders.matricule")
                                                                : selectedAbnType && selectedAbnType?.hasCIN
                                                                ? t("manage_users.client.placeholders.cin")
                                                                : selectedAbnType && selectedAbnType?.is_impersonal && !isImpersonalMoral
                                                                ? t("manage_users.client.placeholders.cin")
                                                                : selectedAbnType && selectedAbnType?.is_impersonal && isImpersonalMoral
                                                                ? t("manage_users.client.placeholders.idImpersonal")
                                                                : selectedAbnType && selectedAbnType?.is_conventional
                                                                ? t("manage_users.client.placeholders.idConventional")
                                                                : t("manage_users.client.placeholders.matricule")
                                                            } disabled={viewMode || isRenewal} />
                                                        </Form.Item>
                                                    </Col>

                                                    <Col span={12}>
                                                        <Form.Item
                                                            label={t("manage_users.labels.address")}
                                                            name="address"
                                                            rules={[{ required: true, message: t("manage_users.errors.addressRequired") }]}
                                                        >
                                                            <Input
                                                                placeholder={
                                                                    selectedAbnType && selectedAbnType?.is_impersonal && isImpersonalMoral
                                                                    ? t("manage_users.client.placeholders.addressImpersonal")
                                                                    : t("manage_users.client.placeholders.address")
                                                                }
                                                                disabled={viewMode || isRenewal}
                                                            />
                                                        </Form.Item>
                                                    </Col>
                                                </Row>


                                                <Row gutter={[16, 16]}>
                                                    <Col xs={24} sm={12}>
                                                        <Form.Item
                                                            label={t("manage_users.labels.phone")}
                                                            name="phone"
                                                            rules={[
                                                                { required: true, message: t("manage_users.errors.phoneRequired") },
                                                                {
                                                                    pattern: /^[0-9]{8}$/,
                                                                    message: t("manage_users.errors.phoneInvalid"),
                                                                },
                                                            ]}
                                                        >
                                                            <Input
                                                                placeholder={t("manage_users.client.placeholders.phone")}
                                                                disabled={viewMode || isRenewal}
                                                            />
                                                        </Form.Item>
                                                    </Col>

                                                    {
                                                        selectedAbnType?.is_impersonal && isImpersonalMoral && (
                                                            <Col span={12}>
                                                                <Form.Item
                                                                    label={t("manage_users.labels.withTVA")}
                                                                    name="is_withTVA"
                                                                >
                                                                    <Switch disabled={viewMode || isRenewal} />
                                                                </Form.Item>
                                                            </Col>
                                                        )
                                                    }

                                                   {
                                                        !(selectedAbnType?.is_impersonal && isImpersonalMoral) && (
                                                            <Col xs={24} sm={12}>
                                                                <Form.Item
                                                                    label={t("manage_users.labels.dob")}
                                                                    name="dob"
                                                                    rules={[
                                                                        { required: true, message: t("manage_users.errors.dobRequired") }
                                                                    ]}
                                                                >
                                                                    <DatePicker
                                                                        className="w-full"
                                                                        format="YYYY-MM-DD"
                                                                        maxDate={dayjs().subtract(6, 'years')}
                                                                        disabled={viewMode || isRenewal}
                                                                        placeholder={t("manage_users.placeholders.dob")}
                                                                        onChange={handleDobChange}
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                        )
                                                    }
                                                </Row>

                                                {
                                                    false && (
                                                        <div className="py-4">
                                                            <Form.Item
                                                                label={t("manage_users.labels.createAccount")}
                                                            >
                                                                <Switch

                                                                    disabled={viewMode || isRenewal}
                                                                    checked={isPasswordEditable}
                                                                    onChange={(checked) => setPasswordEditable(checked)}
                                                                />
                                                            </Form.Item>
                                                        </div>
                                                    )
                                                }

                                                {
                                                    isPasswordEditable ? (
                                                        <Row gutter={16}>
                                                        <Col span={12}>
                                                            <Form.Item
                                                            label={t("manage_users.labels.email")}
                                                            name="email"
                                                            rules={[
                                                                { required: true, type: "email", message: t("manage_users.errors.emailRequired") },
                                                            ]}
                                                            >
                                                            <Input
                                                                placeholder={t("manage_users.client.placeholders.email")}
                                                                disabled={viewMode}
                                                            />
                                                            </Form.Item>
                                                        </Col>

                                                        <Col span={12}>
                                                            <Form.Item
                                                            label={t("manage_users.labels.password")}
                                                            name="password"
                                                            >
                                                            <>
                                                                <div className="flex items-center gap-4">
                                                                <div>
                                                                    <Input
                                                                    placeholder={t("manage_users.admin.placeholders.password")}
                                                                    disabled={!isPasswordEditable}
                                                                    value={form.getFieldValue("password")}
                                                                    onChange={(e) =>
                                                                        form.setFieldsValue({ password: e.target.value })
                                                                    }
                                                                    />
                                                                </div>
                                                                </div>

                                                                {!viewMode && (
                                                                <small style={{ color: "var(--secondary-color)" }}>
                                                                    {t("manage_users.admin.defaultPasswordMessage")}
                                                                </small>
                                                                )}
                                                            </>
                                                            </Form.Item>
                                                        </Col>
                                                        </Row>
                                                ) : null }


                                                {
                                                    selectedAbnType && !loading && (
                                                        <Row gutter={16}>
                                                            <Col xs={24} sm={12}>
                                                                <Form.Item
                                                                    name="id_governorate"
                                                                    label={t("manage_users.labels.governorate")}
                                                                    rules={[{ required: true, message: t("manage_users.errors.governorateRequired") }]}
                                                                >
                                                                    <Select
                                                                        disabled={viewMode || isRenewal}
                                                                        placeholder={t("manage_users.placeholders.governorate")}
                                                                        onChange={handleGovernorateChange}
                                                                        options={governorates?.map((gov: any) => ({
                                                                            label: gov[`nom_${currentLang}`],
                                                                            value: gov.id,
                                                                        }))}
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                            <Col xs={24} sm={12}>
                                                                <Form.Item
                                                                    name="id_delegation"
                                                                    label={t("manage_users.labels.delegation")}
                                                                    rules={[{ required: true, message: t("manage_users.errors.delegationRequired") }]}
                                                                >
                                                                    <Select
                                                                        disabled={viewMode || !selectedFilterGovernorate || isRenewal}
                                                                        placeholder={t("manage_users.placeholders.delegation")}
                                                                        onChange={handleDelegationChange}
                                                                        options={filteredDelegations.map((del: any) => ({
                                                                            label: del[`nom_${currentLang}`],
                                                                            value: del.id,
                                                                        }))}
                                                                        notFoundContent={
                                                                            isDelegationsLoading ? (
                                                                                <div className="flex items-center justify-center py-2">
                                                                                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
                                                                                </div>
                                                                            ) : (
                                                                                <div className="text-center py-2 text-gray-500">
                                                                                    {!selectedFilterGovernorate
                                                                                        ? t("manage_users.selectGovernorate")
                                                                                        : t("common.noData")}
                                                                                </div>
                                                                            )
                                                                        }
                                                                    />
                                                                </Form.Item>
                                                            </Col>
                                                        </Row>
                                                    )
                                                }
                                                {
                                                    selectedAbnType && (selectedAbnType?.is_student || specialClient !== "CIVIL") && !selectedAbnType?.is_impersonal && !selectedAbnType?.is_conventional && !loading && (
                                                        <Row gutter={16}>
                                                            <Col span={12}>
                                                                <Form.Item
                                                                    label={t("manage_users.labels.establishment")}
                                                                    name="id_establishment"
                                                                    rules={[{ required: true, message: t("manage_users.errors.establishmentRequired") }]}
                                                                >
                                                                    <Select
                                                                        disabled={viewMode || isRenewal}
                                                                        placeholder={t("manage_users.client.placeholders.establishment")}
                                                                        onChange={(value) => {
                                                                            const establishment = establishments.find((est: any) => est.id === value);
                                                                            setSelectedEstablishment(establishment);

                                                                            if(selectedStationDepart) {
                                                                                fetchConnectedStations(selectedStationDepart, selectedAbnType.id, establishment?.id_delegation);
                                                                            }
                                                                        }}
                                                                    >
                                                                        {establishments?.map((el:any) => (
                                                                            <Select.Option key={el.id} value={el.id}>{el[`nom_${currentLang}`]}</Select.Option>
                                                                        ))}
                                                                    </Select>
                                                                </Form.Item>
                                                            </Col>
                                                            <Col span={12}>
                                                            <Form.Item
                                                                label={t("manage_users.labels.schoolDegree")}
                                                                name="id_degree"
                                                                rules={[{ required: true, message: t("manage_users.errors.schoolDegreeRequired") }]}
                                                                validateStatus={!dobSelected ? '' : filteredDegrees.length === 0 ? 'error' : ''}
                                                                help={
                                                                    dobSelected && filteredDegrees.length === 0
                                                                    ? t("manage_users.errors.noDegreeForAge")
                                                                    : null
                                                                }
                                                                >
                                                                <Select
                                                                    disabled={!dobSelected || viewMode || isRenewal}
                                                                    placeholder={t("manage_users.client.placeholders.schoolDegree")}
                                                                >
                                                                    {filteredDegrees.map((el: any) => (
                                                                    <Select.Option key={el.id} value={el.id}>
                                                                        {el[`nom_${currentLang}`]}
                                                                    </Select.Option>
                                                                    ))}
                                                                </Select>
                                                                </Form.Item>
                                                            </Col>
                                                        </Row>
                                                    )
                                                }
                                            </>
                                            )}

                                            {
                                                selectedAbnType && !loading && (
                                                    <Divider orientation="left">{t("manage_newSubs.subscriptionInfo")}</Divider>
                                                )
                                            }


                                            <Row gutter={16}>
                                                {
                                                    selectedAbnType && !loading && (
                                                        <>
                                                            <Col span={12}>
                                                                <Form.Item
                                                                    label={t("manage_newSubs.labels.station_depart")}
                                                                    name="station_depart"
                                                                    rules={[{
                                                                        required: true,
                                                                        message: t("manage_newSubs.errors.stationDepartRequired")
                                                                    }]}
                                                                >
                                                                    <Select
                                                                            disabled={viewMode || isRenewal}
                                                                            placeholder={t("manage_newSubs.placeholders.station_depart")}
                                                                            loading={isLoadingStations}
                                                                            notFoundContent={
                                                                                isLoadingStations ? (
                                                                                    <div className="flex justify-center py-2">
                                                                                        <Spin size="small" />
                                                                                    </div>
                                                                                ) : null
                                                                            }
                                                                            onChange={(value) => {
                                                                                // Reset station_arrival and lines when station_depart changes
                                                                                setSelectedStationDepart(value);
                                                                                setSelectedStationArrival(null);
                                                                                setAvailableLines([]);
                                                                                setSelectedLine(null);
                                                                                setLineDetails(null);

                                                                                // Reset form fields
                                                                                form.setFieldsValue({
                                                                                    station_arrival: undefined,
                                                                                    id_line: undefined
                                                                                });

                                                                                if (value && selectedAbnType?.id) {
                                                                                    fetchConnectedStations(value, selectedAbnType.id, selectedEstablishment?.id_delegation);
                                                                                }
                                                                            }}
                                                                    >
                                                                        {isLoadingStations ? (
                                                                            <Select.Option disabled value="loading">
                                                                                <div className="flex items-center gap-2">
                                                                                    <Spin size="small" />
                                                                                    <span>{t("common.loading")}</span>
                                                                                </div>
                                                                            </Select.Option>
                                                                        ) : stations.length > 0 ? (
                                                                            stations.map((stop) => (
                                                                                <Select.Option key={stop.id} value={stop.id}>
                                                                                    {stop[`nom_${currentLang}`] || stop.name}
                                                                                </Select.Option>
                                                                            ))
                                                                        ) : (
                                                                            <Select.Option disabled value="no-data">{t("common.noData")}</Select.Option>
                                                                        )}
                                                                    </Select>
                                                                </Form.Item>
                                                            </Col>
                                                            <Col span={12}>
                                                                <Form.Item
                                                                    label={t("manage_newSubs.labels.station_arrival")}
                                                                    name="station_arrival"
                                                                    rules={[{
                                                                        required: true,
                                                                        message: t("manage_newSubs.errors.stationArrivalRequired")
                                                                    }]}
                                                                >
                                                                    <Select
                                                                            disabled={viewMode || isRenewal || !selectedStationDepart}
                                                                            placeholder={t("manage_routes.placeholders.station_arrival")}
                                                                            onChange={(value) => {
                                                                                setSelectedStationArrival(value);

                                                                                // Reset lines when station_arrival changes
                                                                                setAvailableLines([]);
                                                                                setSelectedLine(null);
                                                                                setLineDetails(null);

                                                                                // Reset line form field
                                                                                form.setFieldsValue({
                                                                                    id_line: undefined
                                                                                });

                                                                                // Update isReversed if we have a line and departure station
                                                                                if (lineDetails && selectedStationDepart) {
                                                                                    updateIsReversedBasedOnStations(lineDetails, selectedStationDepart, value);
                                                                                }

                                                                                // Fetch lines when station_arrival changes
                                                                                if (value && selectedStationDepart && selectedAbnType?.id) {
                                                                                    fetchLinesByStations(selectedStationDepart, value, selectedAbnType.id);
                                                                                }
                                                                            }}
                                                                            loading={isLoadingConnectedStations}
                                                                            notFoundContent={
                                                                                isLoadingConnectedStations ? (
                                                                                    <div className="flex justify-center py-2">
                                                                                        <Spin size="small" />
                                                                                    </div>
                                                                                ) : null
                                                                            }
                                                                    >
                                                                        {isLoadingConnectedStations ? (
                                                                            <Select.Option disabled value="loading">
                                                                                <div className="flex items-center gap-2">
                                                                                    <Spin size="small" />
                                                                                    <span>{t("common.loading")}</span>
                                                                                </div>
                                                                            </Select.Option>
                                                                        ) : connectedStations.length > 0 ? (
                                                                            connectedStations.map((stop) => (
                                                                                <Select.Option key={stop.id} value={stop.id}>
                                                                                    {stop[`nom_${currentLang}`] || stop.name}
                                                                                </Select.Option>
                                                                            ))
                                                                        ) : selectedStationDepart ? (
                                                                            <Select.Option disabled value="no-data">{t("common.noData")}</Select.Option>
                                                                        ) : (
                                                                            <Select.Option disabled value="select-departure">{t("manage_newSubs.selectDepartureFirst")}</Select.Option>
                                                                        )}
                                                                    </Select>
                                                                </Form.Item>
                                                            </Col>
                                                        </>
                                                    )
                                                }
                                            </Row>

                                            {
                                                selectedStationDepart && selectedStationArrival && !loading && (
                                                    <Form.Item
                                                        label={t("manage_newSubs.labels.line")}
                                                        name="id_line"
                                                        rules={[{
                                                            required: true,
                                                            message: t("manage_newSubs.errors.lineRequired")
                                                        }]}
                                                    >
                                                        <Select
                                                                disabled={viewMode || isRenewal}
                                                                placeholder={t("manage_newSubs.placeholders.line")}
                                                                onChange={handleLineChange}
                                                                loading={isLoadingLines}
                                                                notFoundContent={
                                                                    isLoadingLines ? (
                                                                        <div className="flex justify-center py-2">
                                                                            <Spin size="small" />
                                                                        </div>
                                                                    ) : null
                                                                }
                                                                value={selectedLine}
                                                        >
                                                            {isLoadingLines ? (
                                                                <Select.Option disabled value="loading">
                                                                    <div className="flex items-center gap-2">
                                                                        <Spin size="small" />
                                                                        <span>{t("common.loading")}</span>
                                                                    </div>
                                                                </Select.Option>
                                                            ) : availableLines.length > 0 ? (
                                                                availableLines.map((line) => (
                                                                    <Select.Option key={line.id} value={line.id}>
                                                                        {line[`nom_${currentLang}`] || line.name || t("common.loading")}
                                                                    </Select.Option>
                                                                ))
                                                            ) : (
                                                                <Select.Option disabled value="no-data">{t("common.noData")}</Select.Option>
                                                            )}
                                                        </Select>
                                                    </Form.Item>
                                                )
                                            }
                                            {
                                                selectedLine && !loading && (
                                                    <div className="pb-6 mb-6">
                                                        <div className="flex flex-col">
                                                            {isLoadingLineDetails ? (
                                                                <div className="flex justify-center py-12">
                                                                    <Spin tip={t("common.loading")} />
                                                                </div>
                                                            ) : lineDetails ? (
                                                                <LineDisplay
                                                                    isReversed={isReversed}
                                                                    record={lineDetails}
                                                                    selectedStationDepart={selectedStationDepart}
                                                                    selectedStationArrival={selectedStationArrival}
                                                                />
                                                            ) : (
                                                                <div className="text-center py-4 text-gray-500">
                                                                    {t("common.noData")}
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                )
                                            }

                                            {
                                                selectedClient && !loading && (
                                                    <Divider />
                                                )
                                            }




                                            {selectedLine && !loading && (
                                                <Row gutter={[16, 16]} className="mt-8 flex flex-wrap justify-center gap-12">
                                                    <div>
                                                        {selectedAbnType && selectedLine && !loading && (
                                                            <div className="flex flex-col gap-3 mt-6">
                                                                {/* Photo Preview */}
                                                                <div className="relative group flex w-full max-w-xs">
                                                                    <div className="relative flex justify-center w-48 h-48 rounded-xl border-2 border-dashed border-gray-200 bg-gray-50 hover:border-primary-200 transition-all duration-300 shadow-sm">
                                                                        <div className="absolute -top-3 -right-3 bg-red-700/80 backdrop-blur-md text-white px-4 py-2 rounded-full flex items-center gap-2 shadow-sm z-10 hover:scale-105 transition-transform">
                                                                            <InfoIcon className="w-5 h-5" />
                                                                            <span className="text-sm font-medium">
                                                                                {t("manage_newSubs.backgroundRequirement")}
                                                                            </span>
                                                                        </div>

                                                                        {croppedImage ? (
                                                                            <img
                                                                                src={croppedImage}
                                                                                alt="Uploaded preview"
                                                                                className="w-full h-full rounded-xl object-cover"
                                                                            />
                                                                        ) : editingNewSubs?.photo ? (
                                                                            <img
                                                                                src={formatImagePath(editingNewSubs.photo)}
                                                                                alt="Subscription photo"
                                                                                className="w-full h-full rounded-xl object-cover"
                                                                            />
                                                                        ) : (
                                                                            <div className="absolute inset-0 flex flex-col items-center justify-center space-y-3">
                                                                                <div className="p-3 bg-white/80 rounded-full shadow-inner">
                                                                                    <UserIcon className="w-10 h-10 text-primary-500/80" />
                                                                                </div>
                                                                                <span className="text-sm text-gray-600 font-medium text-center px-4">
                                                                                    {t("manage_newSubs.noPhoto")}
                                                                                </span>
                                                                            </div>
                                                                        )}
                                                                    </div>
                                                                </div>

                                                                <Form.Item
                                                                    name="photo"
                                                                    valuePropName="src"
                                                                    className="w-full"
                                                                    rules={[{
                                                                        required: true,
                                                                        message: t("manage_newSubs.errors.photoRequired"),
                                                                    }]}
                                                                >
                                                                    <input type="hidden" value={croppedImage || editingNewSubs?.photo || ''} />
                                                                    <Upload
                                                                        beforeUpload={handleUpload}
                                                                        accept="image/*"
                                                                        showUploadList={false}
                                                                        maxCount={1}
                                                                    >
                                                                        <Button
                                                                            type="dashed"
                                                                            icon={<UploadIcon className="w-3 h-3" />}
                                                                        >
                                                                            {t("manage_newSubs.upload")}
                                                                        </Button>
                                                                    </Upload>
                                                                </Form.Item>
                                                            </div>
                                                        )}
                                                    </div>

                                                    <Divider
                                                        type="vertical"
                                                        style={{ height: 'auto', width: 2 }}
                                                    />

                                                    <Col xs={24} sm={12}>
                                                        <div className="flex flex-col gap-3">
                                                            <div>
                                                                {
                                                                    selectedLine && (selectedAbnType?.is_impersonal || selectedAbnType?.is_conventional) && !loading && (
                                                                        <Form.Item
                                                                            label={t("manage_newSubs.labels.subsNumber")}
                                                                            name="subs_number"
                                                                            rules={[{ required: true, message: t("manage_newSubs.errors.subsNumberRequired") }]}
                                                                        >
                                                                            <Input
                                                                                placeholder={t("manage_newSubs.placeholders.subsNumber")}
                                                                                type="number"
                                                                                disabled={viewMode}
                                                                            />
                                                                        </Form.Item>
                                                                    )
                                                                }
                                                            </div>
                                                            <div>
                                                                {selectedLine && !loading && (
                                                                    <Form.Item
                                                                        label={t("manage_newSubs.labels.periodicity")}
                                                                        name="id_periodicity"
                                                                        rules={[{
                                                                            required: true,
                                                                            message: t("manage_newSubs.errors.periodicityRequired")
                                                                        }]}
                                                                    >
                                                                        <Select
                                                                            disabled={viewMode}
                                                                            placeholder={t("manage_newSubs.placeholders.periodicity")}
                                                                            onChange={(_) => {
                                                                                form.setFieldsValue({ rest_days: [] });
                                                                                setTimeout(() => {
                                                                                    form.validateFields(['rest_days']);
                                                                                }, 0);
                                                                            }}
                                                                        >
                                                                            {periodicities.map((el:any) => (
                                                                                <Select.Option key={el.id} value={el.id}>
                                                                                    {el[`nom_${currentLang}`]}
                                                                                </Select.Option>
                                                                            ))}
                                                                        </Select>
                                                                    </Form.Item>
                                                                )}
                                                            </div>

                                                            <div>
                                                                {selectedLine && !selectedAbnType?.is_student && !loading && (
                                                                    <Form.Item
                                                                        label={t("manage_newSubs.labels.restDays")}
                                                                        name="rest_days"
                                                                        rules={[
                                                                            {
                                                                                required: true,
                                                                                message: t("manage_newSubs.errors.restDaysRequired")
                                                                            },
                                                                            {
                                                                                validator: (_, value) => {
                                                                                    const periodicityId = form.getFieldValue('id_periodicity');
                                                                                    if (!periodicityId || !value) return Promise.resolve();

                                                                                    const selectedPeriodicity = periodicities.find((p: any) => p.id === periodicityId);
                                                                                    if (!selectedPeriodicity) return Promise.resolve();

                                                                                    const maxDaysPerWeek = selectedPeriodicity.max_days_per_week;
                                                                                    setMaxDaysPerWeek(maxDaysPerWeek);
                                                                                    if (!maxDaysPerWeek) return Promise.resolve();

                                                                                    return value.length <= maxDaysPerWeek
                                                                                        ? Promise.resolve()
                                                                                        : Promise.reject(
                                                                                            t("manage_newSubs.errors.tooManyRestDays", {
                                                                                                max: maxDaysPerWeek,
                                                                                                periodicity: selectedPeriodicity[`nom_${currentLang}`]
                                                                                            })
                                                                                        );
                                                                                }
                                                                            }
                                                                        ]}
                                                                    >
                                                                        <Select
                                                                            maxCount={maxDaysPerWeek}
                                                                            mode="multiple"
                                                                            disabled={viewMode}
                                                                            placeholder={t("manage_newSubs.placeholders.restDays")}
                                                                        >
                                                                            {restDaysData.map((el:any) => (
                                                                                <Select.Option key={el.id} value={el.id}>
                                                                                    {el.name}
                                                                                </Select.Option>
                                                                            ))}
                                                                        </Select>
                                                                    </Form.Item>
                                                                )}

                                                                {selectedLine && selectedAbnType?.is_student && !loading && (
                                                                    <Form.Item
                                                                        label={t("manage_newSubs.labels.vacation")}
                                                                        name="hasVacances"
                                                                        rules={[{
                                                                            required: true,
                                                                            message: t("manage_newSubs.errors.vacationRequired")
                                                                        }]}
                                                                    >
                                                                        <Radio.Group disabled={viewMode}>
                                                                            <Radio value={true}>
                                                                                {t("manage_newSubs.options.withVacation")}
                                                                            </Radio>
                                                                            <Radio value={false}>
                                                                                {t("manage_newSubs.options.withoutVacation")}
                                                                            </Radio>
                                                                        </Radio.Group>
                                                                    </Form.Item>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </Col>
                                                </Row>
                                            )}
                                        </>
                                    )
                                }
                            </Col>
                        </Row>
                    </Form>
                </Spin>
            </Modal>

            {/* --------------- Payment Modal --------------- */}
            <PaymentModal
                paymentModal={paymentModal}
                handleReset={handleReset}
                abnRecord={abnRecord}
                loading={loading}
                croppedImage={croppedImage}
                isRenewal={isRenewal}
                onPaymentSuccess={() => {
                    actionRef.current?.reload();
                }}
            />

            {/* --------------- Crop Modal --------------- */}
            <CropModal
                setCroppedImage={setCroppedImage}
                form={form}
                imageSrc={imageSrc}
                cropModalVisible={cropModalVisible}
                setCropModalVisible={setCropModalVisible}
            />

            {/* --------------- Subs Card Print Modal --------------- */}
            <SubsCardPrintModal
                isDuplicated = {isDuplicated}
                isVisible={isCardModalVisible}
                onClose={() => {setIsCardModalVisible(false);setIsDuplicated(false)}}
                abnRecord={abnRecord}
                onPrintSuccess={() => {
                    actionRef.current?.reload();
                }}
            />
        </>
    );
}

export default ManageNewSubs;
