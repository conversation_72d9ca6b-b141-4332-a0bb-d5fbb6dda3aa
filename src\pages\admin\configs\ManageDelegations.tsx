import { useEffect, useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Breadcrumb,
    Row,
    Col, 
    Select,
    Popconfirm,
} from "antd";
import { DeleteOutlined, EditOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {useDispatch} from "react-redux";
import {toast} from "react-toastify";
import {
    deleteDelegation,
    getDelegations,
    storeDelegation,
    updateDelegation
} from "../../../features/admin/delegationSlice.ts";
import {getGovernorateAll} from "../../../features/admin/governorateSlice.ts";
import { hasPermission } from "../../../helpers/permissions.ts";

function ManageDelegations() {
    const {t, i18n} = useTranslation();
    const currentLang = i18n.language;

    const actionRef = useRef<any>();
    const dispatch:any = useDispatch();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingDelegation, setEditingDelegation] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);
    const [governorates, setGovernorates] = useState([]);

    /*|--------------------------------------------------------------------------
    |  - FETCH DATA
    |-------------------------------------------------------------------------- */
    const handleGetDelegations = (params:any, sort:any, filter:any) => {
        return dispatch(getDelegations({
            pageNumber,
            perPage: pageSize,
            params, sort, filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.meta.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });
    }
    const fetchGovernorates = async () => {
        try {
            const response = await dispatch(getGovernorateAll()).unwrap();
            setGovernorates(response.data);
        } catch (error) {
            console.error('Error fetching governorates:', error);
        }
    };
    useEffect(() => {
        fetchGovernorates();
    }, []);

    /*|--------------------------------------------------------------------------
    |  - COLUMNS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_delegations.labels.id")}`,
            dataIndex: "id",
            search: false,
            width: 60,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_delegations.labels.name")}`,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record[`nom_${currentLang}`],
        },
        {
            title: `${t("manage_delegations.labels.governorate")}`,
            dataIndex: "governorate",
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record.governorate[`nom_${currentLang}`] || "-",
            renderFormItem: () => {
                return (
                    <Select
                        showSearch
                        filterOption={(input, option) =>
                            (option?.label as string)?.toLowerCase()?.includes(input.toLowerCase())
                        }
                        allowClear
                        placeholder={t("manage_delegations.filters.governorate")}
                        options={governorates.map((el:any) => ({
                            label: el[`nom_${currentLang}`],
                            value: el.id,
                        }))}
                    />
                )
            }
        },
        {
            title: `${t("manage_delegations.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_delegations.labels.actions")}`,
            fixed: "right",
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {
                        hasPermission("edit_delegations") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                    }
                    {
                        hasPermission("delete_delegations") && (
                            <Popconfirm
                                title={t("manage_delegations.confirmDelete")}
                                onConfirm={() => handleDelete(record.id)}
                                okText={t("manage_delegations.yes")}
                                cancelText={t("manage_delegations.no")}
                            >
                                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                            </Popconfirm>
                        )
                    }
                </div>
            ),
        },
    ];
    const breadcrumbItems:any = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.location")}</Link>,
        },
        {
            title: t("manage_delegations.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingDelegation(record);
        form.setFieldsValue({
            ...record,
            id_governorate: record.governorate?.id,
        });
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit = (record: any) => {
        setEditingDelegation(record);
        form.setFieldsValue({
            ...record,
            id_governorate: record.governorate?.id,
        });
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd = () => {
        setEditingDelegation(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE DELEGATION
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        setLoading(true);
        const payload = editingDelegation ? { id: editingDelegation.id, ...values } : values;
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            if (editingDelegation) {
                await dispatch(updateDelegation(payload)).unwrap();
            } else {
                await dispatch(storeDelegation(values)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_delegations.confirmAction"),
            content: editingDelegation
                ? t("manage_delegations.confirmUpdate")
                : t("manage_delegations.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE DELETE
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteDelegation(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false);
        setViewMode(false);
        form.resetFields();
    };

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />
            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_delegations.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        rowKey={"id"}
                        request={async (params:any, sort:any, filter:any) => {
                            setLoading(true);
                            const dataFilter:any = await handleGetDelegations(params, sort, filter);
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        pagination={{
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_delegations.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                title={
                    viewMode
                        ? t("manage_delegations.details")
                        : editingDelegation
                            ? t("manage_delegations.edit")
                            : t("manage_delegations.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_delegations.save")}
                footer={viewMode ? null : undefined}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_fr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_en")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_ar")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col xs={24} sm={24}>
                            <Form.Item
                                label={t("manage_delegations.labels.governorate")}
                                name="id_governorate"
                                rules={[{ required: true, message: t("manage_delegations.errors.governorateRequired") }]}
                            >
                                <Select
                                    placeholder={t("manage_delegations.placeholders.governorate")}
                                    disabled={viewMode}
                                    notFoundContent={
                                        loading ? (
                                            <div className="flex items-center justify-center py-2">
                                                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
                                            </div>
                                        ) : (
                                            <div className="text-center py-2 text-gray-500">
                                                {!loading
                                                    ? t("manage_establishment.selectGovernorate")
                                                    : t("common.noData")}
                                            </div>
                                        )
                                    }
                                >
                                    {governorates.map((el:any) => (
                                        <Select.Option key={el.id} value={el.id}>
                                            {el[`nom_${currentLang}`]}
                                        </Select.Option>
                                    ))}
                                </Select>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
}

export default ManageDelegations;
