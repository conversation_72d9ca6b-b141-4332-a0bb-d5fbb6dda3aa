import { useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,

    Breadcrumb,
    Row,
    Col, Tag,
    Popconfirm,
} from "antd";
import { DeleteOutlined, EditOutlined, EyeOutlined, TeamOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {PurchaseOrdersManager, SocialAffairsList} from "../../../components";
import {
    deleteGovernorate,
    getGovernorates,
    storeGovernorate,
    updateGovernorate
} from "../../../features/admin/governorateSlice.ts";
import {useDispatch} from "react-redux";
import {toast, ToastContainer} from "react-toastify";
import i18n from "../../../i18n/index.ts";
import { hasPermission } from "../../../helpers/permissions.ts";

function ManageGovernorates() {
    const {t} = useTranslation();
    const currentLang = i18n.language;

    const actionRef:any = useRef<any>();
    const dispatch:any = useDispatch();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingGovernorate, setEditingGovernorate] = useState<any>(null);
    const [form] = Form.useForm();

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    // Social Affairs Modal
    const [socialAffairsModalVisible, setSocialAffairsModalVisible] = useState(false);
    const [selectedGovernorate, setSelectedGovernorate] = useState<any>(null);

    {/*|--------------------------------------------------------------------------
    | FETCH ALL GOVERNORATES WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */}
    const handleGetGovernorates = (params:any,sort:any,filter:any ) =>
    {
        return dispatch(getGovernorates({
            pageNumber,
            perPage: pageSize,
            params,sort,filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.meta.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });
    }

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: `${t("manage_governorates.labels.code")}`,
            dataIndex: "code",
            search: false,
            width: 60,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: t(`manage_governorates.labels.name`),
            sorter: true,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record[`nom_${currentLang}`],
        },
        {
            title: `${t("manage_governorates.labels.hasPO")}`,
            dataIndex: "hasPO",
            sorter: true,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_hasPO: boolean, record: any) => {
                const hasActivePO = record.governorate_purchase_orders?.some((po: any) => po.status === true);
                return hasActivePO ?
                    <Tag color="success">{t("common.yes")}</Tag> :
                    <Tag color="error">{t("common.no")}</Tag>;
            },
        },
        {
            title: t("manage_governorates.labels.purchaseAmount"),
            dataIndex: "purchase_amount",
            render: (_: any, record: any) => {
                return (
                    <div className="font-semibold">{record?.purchase_amount} {t("common.tnd")}</div>
                )
            },
            sorter: true,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },
        {
            title: `${t("manage_governorates.labels.createdAt")}`,
            dataIndex: "created_at",
            valueType: "date",
            sorter: true,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
        },

        {
            title: `${t("manage_governorates.labels.actions")}`,
            fixed: "right",
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {
                            hasPermission("edit_governorates") && (
                                <Button
                                    className="btn-edit"
                                    icon={<EditOutlined />}
                                    onClick={() => handleEdit(record)}
                                />
                            )
                    }
                    <Button
                        className="btn-social-affairs"
                        icon={<TeamOutlined />}
                        onClick={() => handleSocialAffairs(record)}
                        title={t("social_affairs.manage")}
                    />

                    {hasPermission("delete_governorates") && (
                         <Popconfirm
                            title={t("manage_governorates.confirmDelete")}
                            onConfirm={() => handleDelete(record.id)}
                            okText={t("manage_governorates.yes")}
                            cancelText={t("manage_governorates.no")}
                        >
                            <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                        </Popconfirm>
                    )}
                </div>
            ),
        },
    ];

    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.location")}</Link>,
        },
        {
            title: t("manage_governorates.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingGovernorate(record);
        form.setFieldsValue(record);
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit = (record: any) => {
        setEditingGovernorate(record);
        form.setFieldsValue(record);
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd = () => {
        setEditingGovernorate(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };
    const refreshGovernorateData = () => {
        actionRef.current?.reload();
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE MOTIF DUPLICATE
    |-------------------------------------------------------------------------- */
    const handleFormSubmit:any = async (values: any) => {
        setLoading(true);
        const payload = editingGovernorate ? { id: editingGovernorate.id, ...values } : values;
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            if (editingGovernorate) {
                await dispatch(updateGovernorate(payload)).unwrap();
            } else {
                await dispatch(storeGovernorate(values)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_duplicateMotifs.confirmAction"),
            content: editingGovernorate
                ? t("manage_duplicateMotifs.confirmUpdate")
                : t("manage_duplicateMotifs.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE MOTIF DUPLICATE
    |-------------------------------------------------------------------------- */
    const handleDelete:any = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteGovernorate(id)).unwrap()
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false)
        setViewMode(false)
        form.resetFields();
        setLoading(false)
    }

    /*|--------------------------------------------------------------------------
    |  - HANDLE SOCIAL AFFAIRS
    |-------------------------------------------------------------------------- */
    const handleSocialAffairs = (record: any) => {
        setSelectedGovernorate(record);
        setSocialAffairsModalVisible(true);
    };

    const handleCloseSocialAffairs = () => {
        setSocialAffairsModalVisible(false);
        setSelectedGovernorate(null);
    };

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />
            <ToastContainer className="z-50" />
            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_governorates.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        rowKey={"id"}
                        request={async (params:any, sort:any, filter:any) => {
                            setLoading(true)
                            const dataFilter:any = await handleGetGovernorates(params,sort,filter);
                            setLoading(false)
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        loading={loading}
                        expandable={{
                            expandedRowRender: (record:any) => (
                                <PurchaseOrdersManager
                                    record={record.id}
                                    onUpdate={refreshGovernorateData}
                                />
                            ),
                        }}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_governorates.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                title={
                    viewMode
                        ? t("manage_governorates.details")
                        : editingGovernorate
                            ? t("manage_governorates.edit")
                            : t("manage_governorates.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_governorates.save")}
                footer={viewMode ? null : undefined}
                confirmLoading={loading}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                    disabled={loading}
                >
                   <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_fr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_en")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_ar")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={[16, 16]}>
                        <Col xs={24} sm={24}>
                            <Form.Item
                                label={t("manage_governorates.labels.code")}
                                name="code"
                                rules={[{ required: true, message: t("manage_governorates.errors.codeRequired") }]}
                            >
                                <Input
                                    type="number"
                                    placeholder={t("manage_governorates.placeholders.code")}
                                    disabled={viewMode}
                                />
                            </Form.Item>

                        </Col>
                    </Row>
                </Form>
            </Modal>

            {/* Social Affairs Modal */}
            <Modal
                width={1200}
                title={t("social_affairs.title_with_governorate", { governorate: selectedGovernorate?.nom_fr || '' })}
                open={socialAffairsModalVisible}
                onCancel={handleCloseSocialAffairs}
                footer={null}
                destroyOnClose
            >
                {selectedGovernorate && (
                    <SocialAffairsList
                        governorateId={selectedGovernorate.id}
                        governorateName={selectedGovernorate[`nom_${currentLang}`]}
                    />
                )}
            </Modal>
        </>
    )}
export default ManageGovernorates;
