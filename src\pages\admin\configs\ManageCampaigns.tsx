import { useRef, useState } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
    Tag,
    Select,
    Tooltip,
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import {
    getCampaigns,
    storeCampaign,
    updateCampaign,
    deleteCampaign,
    getCampaignSalesPeriods,
} from "../../../features/admin/campaignSlice";
import {
    CampaignPeriodsManager,
    SchoolCampaignTimeline,
} from "../../../components";
import { hasPermission } from "../../../helpers/permissions";

function ManageCampaigns() {
    const { t, i18n } = useTranslation();
    const currentLang = i18n.language;
    const dispatch: any = useDispatch();
    const actionRef = useRef<any>();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingCampaign, setEditingCampaign] = useState<any>(null);
    const [form] = Form.useForm();
    const [salesPeriodsData, setSalesPeriodsData] = useState<any[]>([]);

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);

    /*|--------------------------------------------------------------------------
    | FETCH ALL CAMPAIGNS WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetCampaigns = (params: any, sort: any, filter: any) => {
        return dispatch(
            getCampaigns({
                pageNumber,
                perPage: pageSize,
                params, sort, filter
            })
        )
            .unwrap()
            .then((originalPromiseResult: any) => {
                setTotal(originalPromiseResult.meta.total);
                originalPromiseResult.data.forEach((item: any) => {
                    item.key = item.id;
                });
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError: any) => {
                console.log(rejectedValueOrSerializedError);
            });
    };

    const fetchSalesPeriods :any = async (campaignId: number) => {
        try {
            const result = await dispatch(getCampaignSalesPeriods(campaignId)).unwrap();
            setSalesPeriodsData(result.data || []);
        } catch (error) {
            console.error('Error fetching sales periods:', error);
            toast.error(t("messages.error"));
        }
    };

    /*|--------------------------------------------------------------------------
    |  - VIEWS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: t("manage_campaigns.labels.name"),
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, data: any) => data[`nom_${currentLang}`],
        },
        {
            title: t("manage_campaigns.labels.status"),
            dataIndex: "status",
            responsive: ["xs", "sm", "md", "lg"],
            valueType: "select",
            render: (_: any, record: any) => (
                <Tag color={record.status === true ? 'success' : 'error'}>
                    {record.status === true ? t("manage_campaigns.open") : t("manage_campaigns.closed")}
                </Tag>
            ),
            valueEnum: {
                "1": { 
                    text: t("manage_campaigns.open"),
                    status: 'Success'
                },
                "0": { 
                    text: t("manage_campaigns.closed"),
                    status: 'Error'
                }
            },
        },
        {
            title: t("manage_campaigns.labels.salesPeriods"),
            dataIndex: "sale_periods",
            render: (sale_periods: any[]) => (
                <Tooltip title={t("manage_campaigns.salesPeriodsCount")}>
                    <Tag color={sale_periods?.length > 0 ? "success" : "error"}>{sale_periods?.length || 0} P</Tag>
                </Tooltip>
            ),
            search: false,
        },
        {
            title: t("manage_campaigns.labels.actions"),
            fixed: "right",
            width: 120,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {
                        hasPermission("edit_campaigns") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                    }
                    {
                        hasPermission("delete_campaigns") && (
                            <Popconfirm
                                title={t("manage_campaigns.confirmDelete")}
                                onConfirm={() => handleDelete(record.id)}
                                okText={t("manage_campaigns.yes")}
                                cancelText={t("manage_campaigns.no")}
                            >
                                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                            </Popconfirm>
                        )
                    }
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: (
                <Link className="!bg-white" to="/auth/commercial-dashboard">
                    {t("auth_sidebar.categories.sales")}
                </Link>
            ),
        },
        {
            title: t("manage_campaigns.title"),
        },
    ];


    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = async (record: any) => {
        setEditingCampaign(record);
        form.setFieldsValue(record);
        setViewMode(true);
        setModalVisible(true);
        await fetchSalesPeriods(record.id);
    };
    const handleEdit = (record: any) => {
        setEditingCampaign(record);
        form.setFieldsValue({
            ...record,
            id_campaign_type: record.id_campaign_type,
            status: record.status,
        });
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd = () => {
        setEditingCampaign(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE CAMPAIGN
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        setLoading(true);
        const payload = editingCampaign
            ? { id: editingCampaign.id, ...values }
            : values;
        const toastId = toast.loading(t("messages.loading"), {
            position: "top-center",
        });
        try {
            if (editingCampaign) {
                await dispatch(updateCampaign(payload)).unwrap();
            } else {
                await dispatch(storeCampaign(values)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000,
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            setLoading(false);
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };
    const confirmSubmit:any = (values: any) => {
            const modal:any = Modal.confirm({
                title: t("manage_campaigns.confirmAction"),
                content: editingCampaign
                    ? t("manage_campaigns.confirmUpdate")
                    : t("manage_campaigns.confirmAdd"),
                okText: t("common.yes"),
                cancelText: t("common.no"),
                onOk: async () => {
                    modal.destroy();
                    await handleFormSubmit(values);
                },
                centered: true,
            });
        };
    

    /*|--------------------------------------------------------------------------
    |  - DELETE CAMPAIGN
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: "top-center",
        });
        try {
            await dispatch(deleteCampaign(id)).unwrap();
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000,
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000,
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setEditingCampaign(null);
        setModalVisible(false);
        setViewMode(false);
    };

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_campaigns.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        expandable={{
                            expandedRowRender: (record: any) => (
                                <CampaignPeriodsManager campaignId={record.id} />
                            ),
                        }}
                        request={async (params: any, sort: any, filter: any) => {
                            setLoading(true);
                            const dataFilter: any = await handleGetCampaigns(
                                params,
                                sort,
                                filter
                            );
                            setLoading(false);
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        pagination={{
                            pageSize,
                            total,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, size) => setPageSize(size),
                        }}
                        scroll={{ x: 800 }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        options={{
                            fullScreen: true,
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_campaigns.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            {/* School Campaign Modal */}
            <Modal
                width={800}
                title={
                    viewMode
                        ? t("manage_campaigns.details")
                        : editingCampaign
                            ? t("manage_campaigns.edit")
                            : t("manage_campaigns.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_campaigns.save")}
                footer={viewMode ? null : undefined}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                >
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_fr")}
                                name="nom_fr"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_delegations.errors.nameFrRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_en")}
                                name="nom_en"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_delegations.errors.nameEnRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_ar")}
                                name="nom_ar"
                                rules={[
                                    {
                                        required: true,
                                        message: t("manage_delegations.errors.nameArRequired"),
                                    },
                                ]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row gutter={16}>
                        {!viewMode ? (
                            <Col span={24}>
                                <Form.Item
                                    label={t("manage_campaigns.labels.status")}
                                    name="status"
                                    initialValue={
                                        editingCampaign ? !!editingCampaign.status : false
                                    }
                                    rules={[
                                        {
                                            required: true,
                                            message: t("manage_campaigns.errors.statusRequired"),
                                        },
                                    ]}
                                >
                                    <Select
                                        disabled={viewMode}
                                        options={[
                                            { label: t("manage_campaigns.open"), value: true },
                                            { label: t("manage_campaigns.closed"), value: false },
                                        ]}
                                    />
                                </Form.Item>
                            </Col>
                        ) : (
                            <Col span={24}>
                                <Form.Item name={"status"} label={t("manage_campaigns.labels.status")}>
                                    <div
                                        className="status-indicator p-2 text-center rounded"
                                        style={{
                                            backgroundColor: editingCampaign?.status
                                                ? "var(--secondary-color)"
                                                : "var(--primary-color)",
                                            color: "#fff",
                                        }}
                                    >
                                        {editingCampaign?.status
                                            ? t("manage_campaigns.open")
                                            : t("manage_campaigns.closed")}
                                    </div>
                                </Form.Item>
                            </Col>
                        )}
                    </Row>

                    {viewMode && (
                        <>
                            <Row gutter={16}>
                                <Col span={24}>
                                    <div className="mt-4">
                                        <SchoolCampaignTimeline salesPeriods={salesPeriodsData} />
                                    </div>
                                </Col>
                            </Row>
                        </>
                    )}
                </Form>
            </Modal>
        </>
    );
}

export default ManageCampaigns;
