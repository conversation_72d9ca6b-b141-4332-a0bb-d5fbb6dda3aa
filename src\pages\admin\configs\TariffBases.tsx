import { useRef, useState, useEffect } from "react";
import {
    Button,
    Modal,
    Form,
    Input,
    Popconfirm,
    Breadcrumb,
    Row,
    Col,
    Select,
    InputNumber,
    DatePicker, Tag,
    Switch
} from "antd";
import { EditOutlined, DeleteOutlined, EyeOutlined } from "@ant-design/icons";
import ProTable from "@ant-design/pro-table";
import {Link} from "react-router-dom";
import {useTranslation} from "react-i18next";
import {toast} from "react-toastify";
import {useDispatch} from "react-redux";
import moment from 'moment';
import {
    deleteTarifBase,
    getTarifBases,
    storeTarifBase,
    updateTarifBase
} from "../../../features/admin/tarifBaseSlice.ts";
import { getAbnTypesAll } from "../../../features/admin/abnTypeSlice.ts";
import { useSelector } from "react-redux";
import { hasPermission } from "../../../helpers/permissions.ts";

function TariffBases() {
    const {t, i18n} = useTranslation();
    const currentLang = i18n.language;
    const actionRef = useRef<any>();
    const dispatch = useDispatch();
    const [form] = Form.useForm();

    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [viewMode, setViewMode] = useState(false);
    const [editingTarifBase, setEditingTarifBase] = useState<any>(null);

    const [pageNumber, setPageNumber] = useState<number>(1);
    const [pageSize, setPageSize] = useState<number>(10);
    const [total, setTotal] = useState(1);


    const abnTypes = useSelector((state: any) => state.abnType.items.data);

    /*|--------------------------------------------------------------------------
    | FETCH ALL ABN TYPES
    |-------------------------------------------------------------------------- */
    const fetchStoreData = async () => {
        if(!abnTypes?.length){
            await dispatch(getAbnTypesAll()).unwrap()
        }
    }

    useEffect(() => {
        fetchStoreData();
    }, []);

    /*|--------------------------------------------------------------------------
    | FETCH ALL TARIF BASES WITH PAGINATION & FILTER
    |-------------------------------------------------------------------------- */
    const handleGetTariffBases = (params:any,sort:any,filter:any) =>
        dispatch(getTarifBases({
            pageNumber,
            perPage: pageSize,
            params,sort,filter
        }))
            .unwrap()
            .then((originalPromiseResult:any) => {
                setTotal(originalPromiseResult.total);
                return originalPromiseResult.data;
            })
            .catch((rejectedValueOrSerializedError:any) => {
                console.log(rejectedValueOrSerializedError);
            });

    /*|--------------------------------------------------------------------------
    |  - COLUMNS
    |-------------------------------------------------------------------------- */
    const columns: any = [
        {
            title: t("manage_tariffBase.labels.name"),
            sorter: true,
            dataIndex: `nom_${currentLang}`,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => record[`nom_${currentLang}`],
        },
        {
            title: t("manage_tariffBase.labels.tariffPerKM"),
            dataIndex: "tariffPerKM",
            sorter: true,
            render: (value: any) => `${value} TND/KM`
        },
        {
            title: t("manage_tariffBase.labels.types"),
            responsive: ["xs", "sm", "md", "lg"],
            dataIndex: "id_subs_type",
            render: (_: any, record: any) => {
                console.log("EEEEEEEEEEEEEE",record);
                if(record.for_website) {
                    return (
                        <Tag color="cyan-inverse">
                            {t("manage_tariffBase.labels.forWebsite")}
                        </Tag>
                    )
                }
                return (
                    <Tag color={record.subs_type?.color || "default"}>
                        {record.subs_type?.[`nom_${currentLang}`]}
                    </Tag>
                )
            },
            renderFormItem: () => (
                <Select
                    allowClear
                    placeholder={t("manage_tariffBase.filters.abn_types")}
                    options={abnTypes?.map((el:any) => ({
                        label: el[`nom_${currentLang}`],
                        value: el.id,
                    }))}
                />
            ),
        },
        {
            title: t("manage_tariffBase.labels.date"),
            dataIndex: "date",
            sorter: true,
            valueType: 'date',
        },
        {
            title: t("manage_tariffBase.labels.createdAt"),
            dataIndex: "created_at",
            valueType: "date",
            search: false,
            sorter: true,
        },
        {
            title: t("manage_tariffBase.labels.actions"),
            fixed: "right",
            width: 170,
            search: false,
            responsive: ["xs", "sm", "md", "lg"],
            render: (_: any, record: any) => (
                <div className="flex gap-1">
                    <Button
                        className="btn-view"
                        icon={<EyeOutlined />}
                        onClick={() => handleView(record)}
                    />
                    {
                        hasPermission("edit_tariff_bases") && (
                            <Button
                                className="btn-edit"
                                icon={<EditOutlined />}
                                onClick={() => handleEdit(record)}
                            />
                        )
                    }
                    {
                        hasPermission("delete_tariff_bases") && (
                            <Popconfirm
                                title={t("manage_tariffBase.confirmDelete")}
                                onConfirm={() => handleDelete(record.id)}
                                okText={t("manage_tariffBase.yes")}
                                cancelText={t("manage_tariffBase.no")}
                            >
                                <Button className="btn-delete" icon={<DeleteOutlined />} danger />
                            </Popconfirm>
                        )
                    }
                </div>
            ),
        },
    ];
    const breadcrumbItems = [
        {
            title: <Link className="!bg-white" to="/auth/commercial-dashboard">{t("auth_sidebar.categories.pricing")}</Link>,
        },
        {
            title: t("manage_tariffBase.title"),
        },
    ];

    /*|--------------------------------------------------------------------------
    |  - HANDLE ACTIONS
    |-------------------------------------------------------------------------- */
    const handleView = (record: any) => {
        setEditingTarifBase(record);
        form.setFieldsValue({
            ...record,
            id_subs_type: record.subs_type?.id,
            date: moment(record.date)
        });
        setViewMode(true);
        setModalVisible(true);
    };
    const handleEdit = (record: any) => {
        setEditingTarifBase(record);
        form.setFieldsValue({
            ...record,
            id_subs_type: record.subs_type?.id,
            date: moment(record.date)
        });
        setViewMode(false);
        setModalVisible(true);
    };
    const handleAdd = () => {
        setEditingTarifBase(null);
        form.resetFields();
        setViewMode(false);
        setModalVisible(true);
    };

    /*|--------------------------------------------------------------------------
    |  - ADD OR UPDATE TARIF BASE
    |-------------------------------------------------------------------------- */
    const handleFormSubmit = async (values: any) => {
        setLoading(true);
        // Format the payload with explicit number conversion
        const formattedValues = {
            ...values,
            tariffPerKM: Number(values.tariffPerKM).toFixed(3),
            date: values.date ? values.date.format('YYYY-MM-DD') : null
        };
        
        // Log the payload for debugging
        console.log('Submitting payload:', formattedValues);
        
        const payload = editingTarifBase 
            ? { id: editingTarifBase.id, ...formattedValues } 
            : formattedValues;

        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        
        try {
            if (editingTarifBase) {
                await dispatch(updateTarifBase(payload)).unwrap();
            } else {
                await dispatch(storeTarifBase(formattedValues)).unwrap();
            }
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
            handleReset();
        } catch (error: any) {
            const fieldErrors:any = Object.keys(error.errors || {}).map((field:any) => ({
                name: field,
                errors: [error.errors[field][0]],
            }));
            form.setFields(fieldErrors);
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        } finally {
            setLoading(false);
        }
    };
    const confirmSubmit = (values: any) => {
        const modal = Modal.confirm({
            title: t("manage_tariffBase.confirmAction"),
            content: editingTarifBase
                ? t("manage_tariffBase.confirmUpdate")
                : t("manage_tariffBase.confirmAdd"),
            okText: t("common.yes"),
            cancelText: t("common.no"),
            onOk: async () => {
                modal.destroy();
                await handleFormSubmit(values);
            },
            centered: true,
        });
    };

    /*|--------------------------------------------------------------------------
    |  - DELETE TARIFF BASE
    |-------------------------------------------------------------------------- */
    const handleDelete = async (id: number) => {
        const toastId = toast.loading(t("messages.loading"), {
            position: 'top-center',
        });
        try {
            await dispatch(deleteTarifBase(id)).unwrap()
            toast.update(toastId, {
                render: t("messages.success"),
                type: "success",
                isLoading: false,
                autoClose: 3000
            });
            actionRef.current?.reload();
        } catch (error: any) {
            toast.update(toastId, {
                render: t("messages.error"),
                type: "error",
                isLoading: false,
                autoClose: 3000
            });
        }
    };

    /*|--------------------------------------------------------------------------
    |  - HANDLE RESET
    |-------------------------------------------------------------------------- */
    const handleReset = () => {
        setModalVisible(false)
        form.resetFields();
        setLoading(false);
    }

    return (
        <>
            <Breadcrumb className="mb-5" items={breadcrumbItems} />

            <Row>
                <Col span={24}>
                    <ProTable
                        headerTitle={t("manage_tariffBase.title")}
                        columns={columns}
                        actionRef={actionRef}
                        cardBordered
                        rowKey={"id"}
                        request={async (params:any, sort:any, filter:any) => {
                            setLoading(true)
                            const dataFilter:any = await handleGetTariffBases(params,sort,filter);
                            setLoading(false)
                            return {
                                data: dataFilter,
                                success: true,
                            };
                        }}
                        sortDirections={["ascend", "descend"]}
                        pagination={{
                            pageSize: pageSize,
                            total: total,
                            showSizeChanger: true,
                            onChange: (page) => setPageNumber(page),
                            onShowSizeChange: (_, pageSize) => {
                                setPageSize(pageSize);
                            },
                        }}
                        scroll={{
                            x: 800,
                        }}
                        search={{
                            labelWidth: "auto",
                            className: "bg-[#FAFAFA]",
                        }}
                        options={{
                            fullScreen: true,
                        }}
                        loading={loading}
                        toolBarRender={() => [
                            <Button key="button" onClick={handleAdd} className="btn-add">
                                {t("manage_tariffBase.add")}
                            </Button>,
                        ]}
                    />
                </Col>
            </Row>

            <Modal
                width={900}
                centered={true}
                title={
                    viewMode
                        ? t("manage_tariffBase.details")
                        : editingTarifBase
                            ? t("manage_tariffBase.edit")
                            : t("manage_tariffBase.add")
                }
                open={modalVisible}
                onCancel={() => handleReset()}
                onOk={() => (viewMode ? setModalVisible(false) : form.submit())}
                okText={viewMode ? null : t("manage_tariffBase.save")}
                footer={viewMode ? null : undefined}
                confirmLoading={loading}
            >
                <Form
                    className="form-inputs"
                    form={form}
                    layout="vertical"
                    onFinish={confirmSubmit}
                    disabled={loading}
                >
                    
                    <Row gutter={16}>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_fr")}
                                name="nom_fr"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameFrRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_fr")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_en")}
                                name="nom_en"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameEnRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_en")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={8}>
                            <Form.Item
                                label={t("manage_delegations.labels.name_ar")}
                                name="nom_ar"
                                rules={[{ required: true, message: t("manage_delegations.errors.nameArRequired") }]}
                            >
                                <Input
                                    placeholder={t("manage_delegations.placeholders.name_ar")}
                                    disabled={viewMode}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_tariffBase.labels.tariffPerKM")}
                                name="tariffPerKM"
                                rules={[{ required: true, message: t("manage_tariffBase.errors.tariffPerKMRequired") }]}
                            >
                                <Input
                                    type="number"
                                    placeholder={t("manage_tariffBase.placeholders.tariffPerKM")}
                                    disabled={viewMode}
                                    style={{ width: '100%' }}
                                    min={0}
                                    step={0.001}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_tariffBase.labels.date")}
                                name="date"
                                rules={[{ required: true, message: t("manage_tariffBase.errors.dateRequired") }]}
                            >
                                <DatePicker
                                    style={{ width: '100%' }}
                                    format="YYYY-MM-DD"
                                    disabled={viewMode}
                                    placeholder={t("manage_tariffBase.placeholders.date")}
                                />
                            </Form.Item>
                        </Col>
                    </Row>

                    <Row gutter={16}>
                        <Col xs={24} sm={12}>
                            <Form.Item
                                label={t("manage_tariffBase.labels.forWebsite")}
                                name="for_website"
                                valuePropName="checked"
                                initialValue={false}
                            >
                                <Switch 
                                    disabled={viewMode} 
                                    onChange={(checked) => {
                                        if (checked) {
                                            form.setFieldValue('id_subs_type', null);
                                        }
                                    }}
                                />
                            </Form.Item>
                        </Col>
                        <Col xs={24} sm={12}>
                            <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => 
                                prevValues.for_website !== currentValues.for_website
                            }>
                                {({ getFieldValue }) => {
                                    const forWebsite = getFieldValue('for_website');
                                    
                                    return !forWebsite && (
                                        <Form.Item
                                            label={t("manage_tariffBase.labels.abn_type")}
                                            name="id_subs_type"
                                            rules={[{ required: !forWebsite, message: t("manage_tariffBase.errors.abnTypeRequired") }]}
                                        >
                                            <Select
                                                placeholder={t("manage_tariffBase.placeholders.abn_type")}
                                                disabled={viewMode}
                                                notFoundContent={
                                                    loading ? (
                                                        <div className="flex items-center justify-center py-2">
                                                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-500" />
                                                        </div>
                                                    ) : (
                                                        <div className="text-center py-2 text-gray-500">
                                                            {!loading
                                                                ? t("manage_tariffBase.abn_type")
                                                                : t("common.noData")}
                                                        </div>
                                                    )
                                                }
                                            >
                                                {abnTypes?.map((el:any) => (
                                                    <Select.Option key={el.id} value={el.id}>
                                                        {el[`nom_${currentLang}`]}
                                                    </Select.Option>
                                                ))}
                                            </Select>
                                        </Form.Item>
                                    );
                                }}
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    );
}

export default TariffBases;










